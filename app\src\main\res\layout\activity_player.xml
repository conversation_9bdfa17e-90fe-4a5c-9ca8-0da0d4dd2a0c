<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/player_background"
    tools:context=".ui.player.PlayerActivity">

    <!-- ExoPlayer View -->
    <androidx.media3.ui.PlayerView
        android:id="@+id/player_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:keepScreenOn="true"
        app:controller_layout_id="@layout/custom_player_controls"
        app:resize_mode="fit"
        app:show_buffering="when_playing"
        app:use_controller="true" />

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@color/primary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <!-- Error Message -->
    <TextView
        android:id="@+id/tv_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/error_background"
        android:padding="16dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Error loading stream"
        tools:visibility="visible" />

    <!-- Controls Overlay -->
    <LinearLayout
        android:id="@+id/controls_overlay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/controls_overlay_background"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Top Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- Back Button -->
            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/cd_back_button"
                android:src="@drawable/ic_arrow_back"
                android:tint="@color/white" />

            <!-- Channel Info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_channel_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    tools:text="Channel Name" />

                <TextView
                    android:id="@+id/tv_channel_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:alpha="0.8"
                    tools:text="Entertainment" />

            </LinearLayout>

            <!-- Fullscreen Button -->
            <ImageButton
                android:id="@+id/btn_fullscreen"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/fullscreen"
                android:src="@drawable/ic_fullscreen"
                android:tint="@color/white" />

        </LinearLayout>

        <!-- EPG Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_now_playing"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/primary"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="Now: Movie Title"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_next_program"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:alpha="0.8"
                tools:text="Next: Another Program"
                tools:visibility="visible" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
