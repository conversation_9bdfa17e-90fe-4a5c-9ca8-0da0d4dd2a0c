{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bf78663ddf7e5a17052df8a5e558236f\\transformed\\material3-1.1.2\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,275,389,500,580,672,782,911,1029,1167,1248,1342,1427,1520,1631,1748,1847,1980,2112,2230,2397,2511,2623,2738,2850,2936,3030,3150,3275,3372,3470,3572,3703,3839,3947,4044,4125,4206,4288,4369,4482,4558,4638,4734,4830,4922,5013,5097,5199,5295,5389,5506,5582,5685", "endColumns": "110,108,113,110,79,91,109,128,117,137,80,93,84,92,110,116,98,132,131,117,166,113,111,114,111,85,93,119,124,96,97,101,130,135,107,96,80,80,81,80,112,75,79,95,95,91,90,83,101,95,93,116,75,102,88", "endOffsets": "161,270,384,495,575,667,777,906,1024,1162,1243,1337,1422,1515,1626,1743,1842,1975,2107,2225,2392,2506,2618,2733,2845,2931,3025,3145,3270,3367,3465,3567,3698,3834,3942,4039,4120,4201,4283,4364,4477,4553,4633,4729,4825,4917,5008,5092,5194,5290,5384,5501,5577,5680,5769"}, "to": {"startLines": "50,51,52,53,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,105,159,229,232,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3604,3715,3824,3938,5646,5726,5818,5928,6057,6175,6313,6394,6488,6573,6666,6777,6894,6993,7126,7258,7376,7543,7657,7769,7884,7996,8082,8176,8296,8421,8518,8616,8718,8849,8985,9093,9385,13367,18954,19187,19369,19746,19822,19902,19998,20094,20186,20277,20361,20463,20559,20653,20770,20846,20949", "endColumns": "110,108,113,110,79,91,109,128,117,137,80,93,84,92,110,116,98,132,131,117,166,113,111,114,111,85,93,119,124,96,97,101,130,135,107,96,80,80,81,80,112,75,79,95,95,91,90,83,101,95,93,116,75,102,88", "endOffsets": "3710,3819,3933,4044,5721,5813,5923,6052,6170,6308,6389,6483,6568,6661,6772,6889,6988,7121,7253,7371,7538,7652,7764,7879,7991,8077,8171,8291,8416,8513,8611,8713,8844,8980,9088,9185,9461,13443,19031,19263,19477,19817,19897,19993,20089,20181,20272,20356,20458,20554,20648,20765,20841,20944,21033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7eb203db16c40ca18c3e77706606711f\\transformed\\core-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "59,60,61,62,63,64,65,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4466,4563,4665,4763,4860,4962,5068,19268", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "4558,4660,4758,4855,4957,5063,5174,19364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5c9ad5896dbe4b4ab7793f66459dd36f\\transformed\\exoplayer-core-2.19.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11581,11658,11717,11782,11843,11923,11995,12085,12181", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "11653,11712,11777,11838,11918,11990,12080,12176,12252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c30a6e1576391bb1585dd679e4d5216\\transformed\\navigation-ui-2.7.5\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "220,221", "startColumns": "4,4", "startOffsets": "18179,18284", "endColumns": "104,116", "endOffsets": "18279,18396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8c441e88d76142b996bad90a67757093\\transformed\\exoplayer-ui-2.19.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1768,1880,1997,2066,2150,2220,2296,2391,2490,2555,2619,2672,2730,2778,2839,2903,2970,3032,3098,3160,3217,3281,3346,3412,3464,3524,3598,3672", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1763,1875,1992,2061,2145,2215,2291,2386,2485,2550,2614,2667,2725,2773,2834,2898,2965,3027,3093,3155,3212,3276,3341,3407,3459,3519,3593,3667,3724"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,567,9614,9703,9796,9871,9956,10042,10117,10183,10268,10354,10422,10484,10544,10613,10730,10842,10959,11028,11112,11182,11258,11353,11452,11517,12257,12310,12368,12416,12477,12541,12608,12670,12736,12798,12855,12919,12984,13050,13102,13162,13236,13310", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "375,562,742,9698,9791,9866,9951,10037,10112,10178,10263,10349,10417,10479,10539,10608,10725,10837,10954,11023,11107,11177,11253,11348,11447,11512,11576,12305,12363,12411,12472,12536,12603,12665,12731,12793,12850,12914,12979,13045,13097,13157,13231,13305,13362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c711e708460550b0e6d2719a43fe6f2e\\transformed\\material-1.10.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1036,1127,1204,1265,1356,1419,1482,1541,1610,1673,1727,1835,1893,1955,2009,2082,2203,2287,2378,2518,2595,2671,2802,2889,2965,3018,3072,3138,3208,3285,3368,3448,3519,3594,3672,3743,3844,3929,4018,4113,4206,4278,4350,4446,4498,4584,4651,4735,4825,4887,4951,5014,5084,5178,5280,5369,5469,5526,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "260,335,410,487,586,677,773,885,967,1031,1122,1199,1260,1351,1414,1477,1536,1605,1668,1722,1830,1888,1950,2004,2077,2198,2282,2373,2513,2590,2666,2797,2884,2960,3013,3067,3133,3203,3280,3363,3443,3514,3589,3667,3738,3839,3924,4013,4108,4201,4273,4345,4441,4493,4579,4646,4730,4820,4882,4946,5009,5079,5173,5275,5364,5464,5521,5579,5658"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "747,4049,4124,4199,4276,4375,5179,5275,5387,9550,13448,13539,13799,13860,13951,14014,14077,14136,14205,14268,14322,14430,14488,14550,14604,14677,14798,14882,14973,15113,15190,15266,15397,15484,15560,15613,15667,15733,15803,15880,15963,16043,16114,16189,16267,16338,16439,16524,16613,16708,16801,16873,16945,17041,17093,17179,17246,17330,17420,17482,17546,17609,17679,17773,17875,17964,18064,18121,18707", "endLines": "22,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "907,4119,4194,4271,4370,4461,5270,5382,5464,9609,13534,13611,13855,13946,14009,14072,14131,14200,14263,14317,14425,14483,14545,14599,14672,14793,14877,14968,15108,15185,15261,15392,15479,15555,15608,15662,15728,15798,15875,15958,16038,16109,16184,16262,16333,16434,16519,16608,16703,16796,16868,16940,17036,17088,17174,17241,17325,17415,17477,17541,17604,17674,17768,17870,17959,18059,18116,18174,18781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\3f6383dc1c59a74ffea8dbe469a360a8\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,1026,1125,1237,1322,1428,1548,1628,1703,1794,1887,1979,2073,2173,2266,2368,2463,2554,2645,2724,2831,2935,3031,3138,3241,3350,3506,19036", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "1021,1120,1232,1317,1423,1543,1623,1698,1789,1882,1974,2068,2168,2261,2363,2458,2549,2640,2719,2826,2930,3026,3133,3236,3345,3501,3599,19111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efe0e3583596feeea587a194dc746a51\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "69,70,103,104,106,162,163,222,223,224,225,227,228,231,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5469,5562,9190,9285,9466,13616,13699,18401,18489,18573,18641,18786,18866,19116,19482,19560,19628", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "5557,5641,9280,9380,9545,13694,13794,18484,18568,18636,18702,18861,18949,19182,19555,19623,19741"}}]}]}