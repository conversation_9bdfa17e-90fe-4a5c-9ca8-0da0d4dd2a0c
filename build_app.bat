@echo off
echo Setting up environment for BadBoyz IPTV Player...

REM Set JAVA_HOME to Android Studio's bundled JDK
set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
set PATH=%JAVA_HOME%\bin;%PATH%

echo JAVA_HOME: %JAVA_HOME%
echo Testing Java...
java -version

echo.
echo Building BadBoyz IPTV Player...
echo.

REM Clean and build the project
gradlew.bat clean
gradlew.bat assembleDebug

echo.
echo Build complete! Check the output above for any errors.
echo APK location: app\build\outputs\apk\debug\app-debug.apk
pause
