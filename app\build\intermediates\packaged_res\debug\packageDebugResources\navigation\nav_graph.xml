<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="com.badboyz.iptvplayer.ui.home.HomeFragment"
        android:label="@string/home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/nav_playlists"
        android:name="com.badboyz.iptvplayer.ui.playlists.PlaylistsFragment"
        android:label="@string/my_playlists"
        tools:layout="@layout/fragment_playlists" />

    <fragment
        android:id="@+id/nav_favorites"
        android:name="com.badboyz.iptvplayer.ui.favorites.FavoritesFragment"
        android:label="@string/favorites"
        tools:layout="@layout/fragment_favorites" />

    <fragment
        android:id="@+id/nav_search"
        android:name="com.badboyz.iptvplayer.ui.search.SearchFragment"
        android:label="@string/search"
        tools:layout="@layout/fragment_search" />

    <fragment
        android:id="@+id/nav_settings"
        android:name="com.badboyz.iptvplayer.ui.settings.SettingsFragment"
        android:label="@string/settings"
        tools:layout="@layout/fragment_settings" />

    <fragment
        android:id="@+id/nav_channels"
        android:name="com.badboyz.iptvplayer.ui.channels.ChannelsFragment"
        android:label="@string/channels"
        tools:layout="@layout/fragment_channels">
        <argument
            android:name="playlistId"
            app:argType="long"
            android:defaultValue="0L" />
    </fragment>

</navigation>
