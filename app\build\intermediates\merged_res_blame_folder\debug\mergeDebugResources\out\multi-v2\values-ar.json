{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c30a6e1576391bb1585dd679e4d5216\\transformed\\navigation-ui-2.7.5\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,110", "endOffsets": "160,271"}, "to": {"startLines": "232,233", "startColumns": "4,4", "startOffsets": "19141,19251", "endColumns": "109,110", "endOffsets": "19246,19357"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8c441e88d76142b996bad90a67757093\\transformed\\exoplayer-ui-2.19.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,4006,4072,4124,4181,4252,4323", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,4001,4067,4119,4176,4247,4318,4374"}, "to": {"startLines": "2,11,19,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,444,944,10512,10591,10669,10745,10839,10931,11005,11070,11162,11252,11322,11386,11449,11518,11626,11735,11850,11916,11999,12071,12143,12235,12326,12390,13149,13202,13273,13328,13389,13447,13521,13585,13649,13709,13774,13838,13900,13966,14018,14075,14146,14217", "endLines": "10,18,26,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "439,939,1404,10586,10664,10740,10834,10926,11000,11065,11157,11247,11317,11381,11444,11513,11621,11730,11845,11911,11994,12066,12138,12230,12321,12385,12448,13197,13268,13323,13384,13442,13516,13580,13644,13704,13769,13833,13895,13961,14013,14070,14141,14212,14268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5c9ad5896dbe4b4ab7793f66459dd36f\\transformed\\exoplayer-core-2.19.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "144,145,146,147,148,149,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12453,12518,12577,12644,12706,12788,12869,12970,13065", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "12513,12572,12639,12701,12783,12864,12965,13060,13144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efe0e3583596feeea587a194dc746a51\\transformed\\ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,978,1045,1126,1209,1279,1355,1429", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,973,1040,1121,1204,1274,1350,1424,1545"}, "to": {"startLines": "81,82,115,116,118,174,175,234,235,236,237,239,240,243,247,248,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6295,6384,10095,10190,10364,14507,14588,19362,19446,19527,19598,19745,19826,20071,20420,20496,20570", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "6379,6462,10185,10283,10444,14583,14689,19441,19522,19593,19660,19821,19904,20136,20491,20565,20686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c711e708460550b0e6d2719a43fe6f2e\\transformed\\material-1.10.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1238,1329,1398,1465,1565,1628,1693,1754,1822,1884,1942,2056,2116,2177,2234,2307,2430,2511,2591,2739,2820,2901,3029,3118,3194,3247,3301,3367,3445,3525,3609,3691,3763,3837,3910,3980,4089,4180,4251,4341,4436,4510,4593,4686,4735,4816,4885,4971,5056,5118,5182,5245,5314,5423,5533,5630,5730,5787,5845", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,127,88,75,52,53,65,77,79,83,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1233,1324,1393,1460,1560,1623,1688,1749,1817,1879,1937,2051,2111,2172,2229,2302,2425,2506,2586,2734,2815,2896,3024,3113,3189,3242,3296,3362,3440,3520,3604,3686,3758,3832,3905,3975,4084,4175,4246,4336,4431,4505,4588,4681,4730,4811,4880,4966,5051,5113,5177,5240,5309,5418,5528,5625,5725,5782,5840,5920"}, "to": {"startLines": "27,66,67,68,69,70,78,79,80,119,172,173,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,4873,4951,5027,5111,5203,5998,6099,6218,10449,14347,14438,14694,14761,14861,14924,14989,15050,15118,15180,15238,15352,15412,15473,15530,15603,15726,15807,15887,16035,16116,16197,16325,16414,16490,16543,16597,16663,16741,16821,16905,16987,17059,17133,17206,17276,17385,17476,17547,17637,17732,17806,17889,17982,18031,18112,18181,18267,18352,18414,18478,18541,18610,18719,18829,18926,19026,19083,19665", "endLines": "34,66,67,68,69,70,78,79,80,119,172,173,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,238", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,127,88,75,52,53,65,77,79,83,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79", "endOffsets": "1769,4946,5022,5106,5198,5281,6094,6213,6290,10507,14433,14502,14756,14856,14919,14984,15045,15113,15175,15233,15347,15407,15468,15525,15598,15721,15802,15882,16030,16111,16192,16320,16409,16485,16538,16592,16658,16736,16816,16900,16982,17054,17128,17201,17271,17380,17471,17542,17632,17727,17801,17884,17977,18026,18107,18176,18262,18347,18409,18473,18536,18605,18714,18824,18921,19021,19078,19136,19740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\3f6383dc1c59a74ffea8dbe469a360a8\\transformed\\appcompat-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1774,1882,1986,2093,2175,2276,2390,2470,2549,2640,2733,2825,2919,3019,3112,3207,3300,3391,3485,3564,3669,3767,3865,3973,4073,4176,4331,19989", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "1877,1981,2088,2170,2271,2385,2465,2544,2635,2728,2820,2914,3014,3107,3202,3295,3386,3480,3559,3664,3762,3860,3968,4068,4171,4326,4423,20066"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bf78663ddf7e5a17052df8a5e558236f\\transformed\\material3-1.1.2\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,388,500,575,676,797,931,1049,1188,1271,1372,1460,1556,1669,1791,1897,2036,2173,2306,2454,2576,2692,2812,2927,3016,3110,3229,3349,3445,3544,3648,3785,3928,4031,4128,4204,4278,4358,4439,4536,4611,4691,4788,4887,4982,5078,5161,5263,5359,5457,5591,5666,5763", "endColumns": "113,112,105,111,74,100,120,133,117,138,82,100,87,95,112,121,105,138,136,132,147,121,115,119,114,88,93,118,119,95,98,103,136,142,102,96,75,73,79,80,96,74,79,96,98,94,95,82,101,95,97,133,74,96,88", "endOffsets": "164,277,383,495,570,671,792,926,1044,1183,1266,1367,1455,1551,1664,1786,1892,2031,2168,2301,2449,2571,2687,2807,2922,3011,3105,3224,3344,3440,3539,3643,3780,3923,4026,4123,4199,4273,4353,4434,4531,4606,4686,4783,4882,4977,5073,5156,5258,5354,5452,5586,5661,5758,5847"}, "to": {"startLines": "62,63,64,65,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,117,171,241,244,246,250,251,252,253,254,255,256,257,258,259,260,261,262,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4428,4542,4655,4761,6467,6542,6643,6764,6898,7016,7155,7238,7339,7427,7523,7636,7758,7864,8003,8140,8273,8421,8543,8659,8779,8894,8983,9077,9196,9316,9412,9511,9615,9752,9895,9998,10288,14273,19909,20141,20323,20691,20766,20846,20943,21042,21137,21233,21316,21418,21514,21612,21746,21821,21918", "endColumns": "113,112,105,111,74,100,120,133,117,138,82,100,87,95,112,121,105,138,136,132,147,121,115,119,114,88,93,118,119,95,98,103,136,142,102,96,75,73,79,80,96,74,79,96,98,94,95,82,101,95,97,133,74,96,88", "endOffsets": "4537,4650,4756,4868,6537,6638,6759,6893,7011,7150,7233,7334,7422,7518,7631,7753,7859,7998,8135,8268,8416,8538,8654,8774,8889,8978,9072,9191,9311,9407,9506,9610,9747,9890,9993,10090,10359,14342,19984,20217,20415,20761,20841,20938,21037,21132,21228,21311,21413,21509,21607,21741,21816,21913,22002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7eb203db16c40ca18c3e77706606711f\\transformed\\core-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "71,72,73,74,75,76,77,245", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5286,5379,5481,5576,5679,5782,5884,20222", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "5374,5476,5571,5674,5777,5879,5993,20318"}}]}]}