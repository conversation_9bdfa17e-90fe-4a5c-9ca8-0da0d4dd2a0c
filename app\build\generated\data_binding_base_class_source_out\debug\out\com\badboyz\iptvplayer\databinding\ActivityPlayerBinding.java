// Generated by view binder compiler. Do not edit!
package com.badboyz.iptvplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.media3.ui.PlayerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.badboyz.iptvplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPlayerBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnFullscreen;

  @NonNull
  public final LinearLayout controlsOverlay;

  @NonNull
  public final PlayerView playerView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvChannelGroup;

  @NonNull
  public final TextView tvChannelName;

  @NonNull
  public final TextView tvError;

  @NonNull
  public final TextView tvNextProgram;

  @NonNull
  public final TextView tvNowPlaying;

  private ActivityPlayerBinding(@NonNull ConstraintLayout rootView, @NonNull ImageButton btnBack,
      @NonNull ImageButton btnFullscreen, @NonNull LinearLayout controlsOverlay,
      @NonNull PlayerView playerView, @NonNull ProgressBar progressBar,
      @NonNull TextView tvChannelGroup, @NonNull TextView tvChannelName, @NonNull TextView tvError,
      @NonNull TextView tvNextProgram, @NonNull TextView tvNowPlaying) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnFullscreen = btnFullscreen;
    this.controlsOverlay = controlsOverlay;
    this.playerView = playerView;
    this.progressBar = progressBar;
    this.tvChannelGroup = tvChannelGroup;
    this.tvChannelName = tvChannelName;
    this.tvError = tvError;
    this.tvNextProgram = tvNextProgram;
    this.tvNowPlaying = tvNowPlaying;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPlayerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPlayerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_player, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPlayerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_fullscreen;
      ImageButton btnFullscreen = ViewBindings.findChildViewById(rootView, id);
      if (btnFullscreen == null) {
        break missingId;
      }

      id = R.id.controls_overlay;
      LinearLayout controlsOverlay = ViewBindings.findChildViewById(rootView, id);
      if (controlsOverlay == null) {
        break missingId;
      }

      id = R.id.player_view;
      PlayerView playerView = ViewBindings.findChildViewById(rootView, id);
      if (playerView == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_channel_group;
      TextView tvChannelGroup = ViewBindings.findChildViewById(rootView, id);
      if (tvChannelGroup == null) {
        break missingId;
      }

      id = R.id.tv_channel_name;
      TextView tvChannelName = ViewBindings.findChildViewById(rootView, id);
      if (tvChannelName == null) {
        break missingId;
      }

      id = R.id.tv_error;
      TextView tvError = ViewBindings.findChildViewById(rootView, id);
      if (tvError == null) {
        break missingId;
      }

      id = R.id.tv_next_program;
      TextView tvNextProgram = ViewBindings.findChildViewById(rootView, id);
      if (tvNextProgram == null) {
        break missingId;
      }

      id = R.id.tv_now_playing;
      TextView tvNowPlaying = ViewBindings.findChildViewById(rootView, id);
      if (tvNowPlaying == null) {
        break missingId;
      }

      return new ActivityPlayerBinding((ConstraintLayout) rootView, btnBack, btnFullscreen,
          controlsOverlay, playerView, progressBar, tvChannelGroup, tvChannelName, tvError,
          tvNextProgram, tvNowPlaying);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
