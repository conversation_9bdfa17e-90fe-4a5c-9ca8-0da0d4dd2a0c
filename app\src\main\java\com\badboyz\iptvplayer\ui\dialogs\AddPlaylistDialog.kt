package com.badboyz.iptvplayer.ui.dialogs

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.badboyz.iptvplayer.R
import com.badboyz.iptvplayer.databinding.DialogAddPlaylistBinding
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class AddPlaylistDialog(
    private val onPlaylistAdded: (name: String, url: String) -> Unit
) : DialogFragment() {
    
    private var _binding: DialogAddPlaylistBinding? = null
    private val binding get() = _binding!!
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogAddPlaylistBinding.inflate(layoutInflater)
        
        return MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.add_playlist_title)
            .setView(binding.root)
            .setPositiveButton(R.string.save) { _, _ ->
                val name = binding.etPlaylistName.text.toString().trim()
                val url = binding.etPlaylistUrl.text.toString().trim()
                
                if (validateInput(name, url)) {
                    onPlaylistAdded(name, url)
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .create()
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return if (showsDialog) {
            super.onCreateView(inflater, container, savedInstanceState)
        } else {
            binding.root
        }
    }
    
    private fun validateInput(name: String, url: String): Boolean {
        when {
            name.isEmpty() -> {
                binding.tilPlaylistName.error = getString(R.string.error_empty_name)
                return false
            }
            url.isEmpty() -> {
                binding.tilPlaylistUrl.error = getString(R.string.error_empty_url)
                return false
            }
            !isValidUrl(url) -> {
                binding.tilPlaylistUrl.error = getString(R.string.error_invalid_url)
                return false
            }
            else -> {
                binding.tilPlaylistName.error = null
                binding.tilPlaylistUrl.error = null
                return true
            }
        }
    }
    
    private fun isValidUrl(url: String): Boolean {
        return url.startsWith("http://") || url.startsWith("https://") || url.endsWith(".m3u")
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
