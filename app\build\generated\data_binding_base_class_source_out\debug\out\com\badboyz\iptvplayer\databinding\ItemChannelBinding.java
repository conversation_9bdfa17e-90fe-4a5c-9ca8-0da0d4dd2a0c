// Generated by view binder compiler. Do not edit!
package com.badboyz.iptvplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.badboyz.iptvplayer.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemChannelBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnFavorite;

  @NonNull
  public final ImageView ivChannelLogo;

  @NonNull
  public final TextView tvChannelGroup;

  @NonNull
  public final TextView tvChannelName;

  @NonNull
  public final TextView tvLiveIndicator;

  @NonNull
  public final TextView tvNowPlaying;

  private ItemChannelBinding(@NonNull MaterialCardView rootView, @NonNull ImageButton btnFavorite,
      @NonNull ImageView ivChannelLogo, @NonNull TextView tvChannelGroup,
      @NonNull TextView tvChannelName, @NonNull TextView tvLiveIndicator,
      @NonNull TextView tvNowPlaying) {
    this.rootView = rootView;
    this.btnFavorite = btnFavorite;
    this.ivChannelLogo = ivChannelLogo;
    this.tvChannelGroup = tvChannelGroup;
    this.tvChannelName = tvChannelName;
    this.tvLiveIndicator = tvLiveIndicator;
    this.tvNowPlaying = tvNowPlaying;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemChannelBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemChannelBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_channel, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemChannelBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_favorite;
      ImageButton btnFavorite = ViewBindings.findChildViewById(rootView, id);
      if (btnFavorite == null) {
        break missingId;
      }

      id = R.id.iv_channel_logo;
      ImageView ivChannelLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivChannelLogo == null) {
        break missingId;
      }

      id = R.id.tv_channel_group;
      TextView tvChannelGroup = ViewBindings.findChildViewById(rootView, id);
      if (tvChannelGroup == null) {
        break missingId;
      }

      id = R.id.tv_channel_name;
      TextView tvChannelName = ViewBindings.findChildViewById(rootView, id);
      if (tvChannelName == null) {
        break missingId;
      }

      id = R.id.tv_live_indicator;
      TextView tvLiveIndicator = ViewBindings.findChildViewById(rootView, id);
      if (tvLiveIndicator == null) {
        break missingId;
      }

      id = R.id.tv_now_playing;
      TextView tvNowPlaying = ViewBindings.findChildViewById(rootView, id);
      if (tvNowPlaying == null) {
        break missingId;
      }

      return new ItemChannelBinding((MaterialCardView) rootView, btnFavorite, ivChannelLogo,
          tvChannelGroup, tvChannelName, tvLiveIndicator, tvNowPlaying);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
