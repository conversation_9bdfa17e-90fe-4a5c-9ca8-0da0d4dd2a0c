R_DEF: Internal format may change without notice
local
anim slide_in_right
anim slide_out_left
color accent
color background_primary
color background_secondary
color background_tertiary
color black
color bottom_nav_color
color card_background
color card_background_selected
color card_stroke
color error
color gradient_end
color gradient_start
color info
color overlay_dark
color overlay_light
color player_background
color player_controls_background
color player_progress
color player_progress_background
color primary
color primary_dark
color primary_light
color purple_200
color purple_500
color purple_700
color success
color surface
color surface_variant
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color text_tertiary
color transparent
color warning
color white
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen button_corner_radius
dimen button_height
dimen card_corner_radius
dimen card_elevation
dimen card_margin
dimen card_padding
dimen channel_item_height
dimen channel_item_padding
dimen channel_logo_size
dimen fab_margin
dimen input_field_height
dimen list_item_height
dimen list_item_padding_horizontal
dimen list_item_padding_vertical
dimen nav_header_height
dimen nav_header_vertical_spacing
dimen player_control_margin
dimen player_control_size
dimen spacing_large
dimen spacing_medium
dimen spacing_small
dimen spacing_tiny
dimen spacing_xlarge
dimen text_size_caption
dimen text_size_large
dimen text_size_medium
dimen text_size_small
drawable channel_card_background
drawable channel_logo_background
drawable controls_overlay_background
drawable error_background
drawable gradient_background
drawable ic_add
drawable ic_app_logo
drawable ic_arrow_back
drawable ic_favorite
drawable ic_favorite_border
drawable ic_fullscreen
drawable ic_home
drawable ic_info
drawable ic_link
drawable ic_menu
drawable ic_more_vert
drawable ic_playlist
drawable ic_search
drawable ic_settings
drawable ic_tv
drawable live_indicator_background
drawable splash_background
drawable status_indicator_active
font roboto_bold
font roboto_medium
font roboto_regular
id action_search
id action_settings
id app_bar_layout
id bottom_navigation
id btn_back
id btn_favorite
id btn_fullscreen
id btn_playlist_menu
id card_add_playlist
id card_favorites
id controls_overlay
id drawer_layout
id et_playlist_name
id et_playlist_url
id fab_add_playlist
id imageView
id iv_channel_logo
id iv_playlist_icon
id nav_about
id nav_channels
id nav_favorites
id nav_graph
id nav_home
id nav_host_fragment
id nav_playlists
id nav_search
id nav_settings
id nav_view
id player_view
id progress_bar
id rv_playlists
id rv_recently_watched
id status_indicator
id textView
id til_playlist_name
id til_playlist_url
id toolbar
id tv_channel_count
id tv_channel_group
id tv_channel_name
id tv_error
id tv_last_updated
id tv_live_indicator
id tv_next_program
id tv_now_playing
id tv_playlist_name
layout activity_main
layout activity_player
layout custom_player_controls
layout dialog_add_playlist
layout fragment_home
layout item_channel
layout item_channel_horizontal
layout item_playlist
layout nav_header_main
menu bottom_nav_menu
menu drawer_menu
menu main_menu
navigation nav_graph
string about
string add_playlist
string add_playlist_title
string add_to_favorites
string all_channels
string app_name
string appearance_settings
string auto_theme
string buffer_size
string buffering
string cancel
string cd_app_logo
string cd_back_button
string cd_channel_logo
string cd_favorite_button
string cd_menu_button
string cd_play_button
string cd_playlist_icon
string cd_search_button
string cd_settings_button
string channel_groups
string channel_info
string channels
string clear_search
string confirm_delete_channel
string confirm_delete_playlist
string connecting
string connection_error
string connection_timeout
string dark_theme
string date_format
string delete
string delete_playlist
string duration_format
string edit
string edit_playlist_title
string error_empty_name
string error_empty_url
string error_invalid_url
string error_loading_playlist
string error_network
string error_playing_channel
string error_unknown
string exit_fullscreen
string favorites
string fullscreen
string general_settings
string home
string light_theme
string live
string loading
string loading_channels
string loading_stream
string mute
string my_playlists
string next_program
string no
string no_channels_found
string no_internet
string no_search_results
string now_playing
string ok
string other
string pause
string play
string playback_error
string player_settings
string playlist_added_successfully
string playlist_deleted_successfully
string playlist_name
string playlist_refreshed_successfully
string playlist_updated_successfully
string playlist_url
string playlist_url_hint
string recently_watched
string refresh
string refresh_playlist
string remove_from_favorites
string save
string search
string search_channels
string search_results
string settings
string stop
string stream_error
string theme
string time_format_24h
string unmute
string user_agent
string version
string volume
string welcome_subtitle
string welcome_title
string yes
style BottomNavigation.Primary
style Button.Primary
style Button.Secondary
style CardView.Channel
style CardView.Playlist
style TextAppearance.Body
style TextAppearance.Caption
style TextAppearance.Subtitle
style TextAppearance.Title
style TextInputLayout.Outlined
style Theme.BadBoyzIPTVPlayer
style Theme.BadBoyzIPTVPlayer.Fullscreen
style Theme.BadBoyzIPTVPlayer.NoActionBar
style Theme.BadBoyzIPTVPlayer.Splash
style Toolbar.Primary
style WindowAnimationTransition
xml backup_rules
xml data_extraction_rules
