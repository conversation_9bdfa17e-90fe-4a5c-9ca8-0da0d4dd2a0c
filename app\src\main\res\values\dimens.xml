<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    
    <!-- Card dimensions -->
    <dimen name="card_margin">8dp</dimen>
    <dimen name="card_padding">16dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    
    <!-- Channel item dimensions -->
    <dimen name="channel_logo_size">56dp</dimen>
    <dimen name="channel_item_height">80dp</dimen>
    <dimen name="channel_item_padding">16dp</dimen>
    
    <!-- Player dimensions -->
    <dimen name="player_control_size">48dp</dimen>
    <dimen name="player_control_margin">16dp</dimen>
    
    <!-- Text sizes -->
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <dimen name="text_size_caption">12sp</dimen>
    
    <!-- Spacing -->
    <dimen name="spacing_tiny">4dp</dimen>
    <dimen name="spacing_small">8dp</dimen>
    <dimen name="spacing_medium">16dp</dimen>
    <dimen name="spacing_large">24dp</dimen>
    <dimen name="spacing_xlarge">32dp</dimen>
    
    <!-- Button dimensions -->
    <dimen name="button_height">48dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    
    <!-- Input field dimensions -->
    <dimen name="input_field_height">56dp</dimen>
    
    <!-- List item dimensions -->
    <dimen name="list_item_height">72dp</dimen>
    <dimen name="list_item_padding_horizontal">16dp</dimen>
    <dimen name="list_item_padding_vertical">12dp</dimen>
</resources>
