{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efe0e3583596feeea587a194dc746a51\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1012,1080,1161,1243,1318,1397,1467", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1007,1075,1156,1238,1313,1392,1462,1585"}, "to": {"startLines": "69,70,103,104,106,162,163,222,223,224,225,227,228,231,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5734,5833,9701,9802,9990,14329,14410,19327,19416,19506,19576,19735,19816,20070,20430,20509,20579", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "5828,5914,9797,9902,10077,14405,14499,19411,19501,19571,19639,19811,19893,20140,20504,20574,20697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c30a6e1576391bb1585dd679e4d5216\\transformed\\navigation-ui-2.7.5\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,121", "endOffsets": "167,289"}, "to": {"startLines": "220,221", "startColumns": "4,4", "startOffsets": "19088,19205", "endColumns": "116,121", "endOffsets": "19200,19322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c711e708460550b0e6d2719a43fe6f2e\\transformed\\material-1.10.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1083,1183,1265,1328,1419,1482,1547,1609,1678,1740,1794,1932,1989,2050,2104,2177,2330,2415,2499,2638,2719,2804,2945,3035,3121,3176,3227,3293,3371,3456,3541,3624,3696,3776,3856,3927,4034,4126,4198,4295,4392,4466,4540,4642,4698,4785,4857,4945,5037,5099,5163,5226,5296,5412,5521,5630,5735,5794,5849", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,140,89,85,54,50,65,77,84,84,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90", "endOffsets": "265,353,439,524,620,707,809,926,1012,1078,1178,1260,1323,1414,1477,1542,1604,1673,1735,1789,1927,1984,2045,2099,2172,2325,2410,2494,2633,2714,2799,2940,3030,3116,3171,3222,3288,3366,3451,3536,3619,3691,3771,3851,3922,4029,4121,4193,4290,4387,4461,4535,4637,4693,4780,4852,4940,5032,5094,5158,5221,5291,5407,5516,5625,5730,5789,5844,5935"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "833,4252,4340,4426,4511,4607,5429,5531,5648,10082,14147,14247,14504,14567,14658,14721,14786,14848,14917,14979,15033,15171,15228,15289,15343,15416,15569,15654,15738,15877,15958,16043,16184,16274,16360,16415,16466,16532,16610,16695,16780,16863,16935,17015,17095,17166,17273,17365,17437,17534,17631,17705,17779,17881,17937,18024,18096,18184,18276,18338,18402,18465,18535,18651,18760,18869,18974,19033,19644", "endLines": "22,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,140,89,85,54,50,65,77,84,84,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90", "endOffsets": "998,4335,4421,4506,4602,4689,5526,5643,5729,10143,14242,14324,14562,14653,14716,14781,14843,14912,14974,15028,15166,15223,15284,15338,15411,15564,15649,15733,15872,15953,16038,16179,16269,16355,16410,16461,16527,16605,16690,16775,16858,16930,17010,17090,17161,17268,17360,17432,17529,17626,17700,17774,17876,17932,18019,18091,18179,18271,18333,18397,18460,18530,18646,18755,18864,18969,19028,19083,19730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bf78663ddf7e5a17052df8a5e558236f\\transformed\\material3-1.1.2\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,405,523,603,702,819,966,1090,1244,1330,1426,1521,1622,1736,1858,1959,2097,2229,2369,2545,2679,2795,2919,3040,3136,3231,3363,3496,3598,3700,3806,3945,4094,4204,4305,4388,4467,4553,4638,4737,4813,4892,4987,5085,5178,5272,5355,5457,5552,5649,5766,5842,5944", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "170,290,400,518,598,697,814,961,1085,1239,1325,1421,1516,1617,1731,1853,1954,2092,2224,2364,2540,2674,2790,2914,3035,3131,3226,3358,3491,3593,3695,3801,3940,4089,4199,4300,4383,4462,4548,4633,4732,4808,4887,4982,5080,5173,5267,5350,5452,5547,5644,5761,5837,5939,6042"}, "to": {"startLines": "50,51,52,53,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,105,159,229,232,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3784,3904,4024,4134,5919,5999,6098,6215,6362,6486,6640,6726,6822,6917,7018,7132,7254,7355,7493,7625,7765,7941,8075,8191,8315,8436,8532,8627,8759,8892,8994,9096,9202,9341,9490,9600,9907,14068,19898,20145,20331,20702,20778,20857,20952,21050,21143,21237,21320,21422,21517,21614,21731,21807,21909", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "3899,4019,4129,4247,5994,6093,6210,6357,6481,6635,6721,6817,6912,7013,7127,7249,7350,7488,7620,7760,7936,8070,8186,8310,8431,8527,8622,8754,8887,8989,9091,9197,9336,9485,9595,9696,9985,14142,19979,20225,20425,20773,20852,20947,21045,21138,21232,21315,21417,21512,21609,21726,21802,21904,22007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7eb203db16c40ca18c3e77706606711f\\transformed\\core-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "59,60,61,62,63,64,65,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4694,4792,4895,4995,5098,5206,5312,20230", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "4787,4890,4990,5093,5201,5307,5424,20326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8c441e88d76142b996bad90a67757093\\transformed\\exoplayer-ui-2.19.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,510,738,826,915,997,1080,1172,1269,1335,1431,1527,1592,1662,1727,1801,1923,2046,2169,2239,2322,2394,2491,2596,2700,2766,2841,2894,2952,3006,3067,3132,3201,3266,3338,3400,3460,3525,3592,3659,3717,3783,3863,3943", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,121,122,122,69,82,71,96,104,103,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "282,505,733,821,910,992,1075,1167,1264,1330,1426,1522,1587,1657,1722,1796,1918,2041,2164,2234,2317,2389,2486,2591,2695,2761,2836,2889,2947,3001,3062,3127,3196,3261,3333,3395,3455,3520,3587,3654,3712,3778,3858,3938,3992"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,605,10148,10236,10325,10407,10490,10582,10679,10745,10841,10937,11002,11072,11137,11211,11333,11456,11579,11649,11732,11804,11901,12006,12110,12176,12912,12965,13023,13077,13138,13203,13272,13337,13409,13471,13531,13596,13663,13730,13788,13854,13934,14014", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,121,122,122,69,82,71,96,104,103,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "377,600,828,10231,10320,10402,10485,10577,10674,10740,10836,10932,10997,11067,11132,11206,11328,11451,11574,11644,11727,11799,11896,12001,12105,12171,12246,12960,13018,13072,13133,13198,13267,13332,13404,13466,13526,13591,13658,13725,13783,13849,13929,14009,14063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\3f6383dc1c59a74ffea8dbe469a360a8\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1003,1121,1232,1349,1434,1540,1663,1752,1837,1928,2021,2116,2210,2310,2403,2498,2595,2686,2777,2862,2973,3082,3184,3295,3405,3513,3684,19984", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "1116,1227,1344,1429,1535,1658,1747,1832,1923,2016,2111,2205,2305,2398,2493,2590,2681,2772,2857,2968,3077,3179,3290,3400,3508,3679,3779,20065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5c9ad5896dbe4b4ab7793f66459dd36f\\transformed\\exoplayer-core-2.19.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,184,242,305,379,455,554,649", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "121,179,237,300,374,450,549,644,711"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12251,12322,12380,12438,12501,12575,12651,12750,12845", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "12317,12375,12433,12496,12570,12646,12745,12840,12907"}}]}]}