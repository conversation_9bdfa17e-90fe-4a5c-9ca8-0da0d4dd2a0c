package com.badboyz.iptvplayer.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "channels")
data class Channel(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val url: String,
    val logo: String? = null,
    val group: String? = null,
    val tvgId: String? = null,
    val tvgName: String? = null,
    val tvgLogo: String? = null,
    val groupTitle: String? = null,
    val language: String? = null,
    val country: String? = null,
    val isFavorite: Boolean = false,
    val isHidden: Boolean = false,
    val playlistId: Long = 0,
    val sortOrder: Int = 0,
    val lastWatched: Long = 0,
    val watchCount: Int = 0,
    val userAgent: String? = null,
    val referer: String? = null,
    val headers: String? = null
) : Parcelable {
    
    fun getDisplayName(): String {
        return tvgName?.takeIf { it.isNotBlank() } ?: name
    }
    
    fun getDisplayLogo(): String? {
        return tvgLogo?.takeIf { it.isNotBlank() } ?: logo
    }
    
    fun getDisplayGroup(): String {
        return groupTitle?.takeIf { it.isNotBlank() } ?: group ?: "Uncategorized"
    }
}
