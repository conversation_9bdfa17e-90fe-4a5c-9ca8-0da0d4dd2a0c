<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.badboyz.iptvplayer"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="31" />

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- Wake lock for video playback -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Audio focus -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <permission
        android:name="com.badboyz.iptvplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.badboyz.iptvplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.badboyz.iptvplayer.IPTVApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.BadBoyzIPTVPlayer"
        android:usesCleartextTraffic="true" >

        <!-- Main Activity -->
        <activity
            android:name="com.badboyz.iptvplayer.ui.main.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.BadBoyzIPTVPlayer.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Player Activity -->
        <activity
            android:name="com.badboyz.iptvplayer.ui.player.PlayerActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.BadBoyzIPTVPlayer.Fullscreen" />

        <!-- Splash Activity -->
        <activity
            android:name="com.badboyz.iptvplayer.ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.BadBoyzIPTVPlayer.Splash" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="*"
                    android:pathPattern=".*\\.m3u"
                    android:scheme="http" />
                <data
                    android:host="*"
                    android:pathPattern=".*\\.m3u"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <!-- Settings Activity -->
        <activity
            android:name="com.badboyz.iptvplayer.ui.settings.SettingsActivity"
            android:exported="false"
            android:parentActivityName="com.badboyz.iptvplayer.ui.main.MainActivity" />
        <activity
            android:name="com.karumi.dexter.DexterActivity"
            android:theme="@style/Dexter.Internal.Theme.Transparent" />
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />
        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.badboyz.iptvplayer.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>