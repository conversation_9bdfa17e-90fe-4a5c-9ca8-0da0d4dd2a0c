<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@color/card_background"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeColor="@color/card_stroke"
    app:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Channel Logo -->
        <ImageView
            android:id="@+id/iv_channel_logo"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/channel_logo_background"
            android:contentDescription="@string/cd_channel_logo"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_tv" />

        <!-- Channel Name -->
        <TextView
            android:id="@+id/tv_channel_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Body"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/btn_favorite"
            app:layout_constraintStart_toEndOf="@id/iv_channel_logo"
            app:layout_constraintTop_toTopOf="@id/iv_channel_logo"
            tools:text="Channel Name" />

        <!-- Channel Group -->
        <TextView
            android:id="@+id/tv_channel_group"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Caption"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@id/btn_favorite"
            app:layout_constraintStart_toEndOf="@id/iv_channel_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_channel_name"
            tools:text="Entertainment" />

        <!-- Now Playing -->
        <TextView
            android:id="@+id/tv_now_playing"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Caption"
            android:textSize="11sp"
            android:textColor="@color/primary"
            app:layout_constraintEnd_toStartOf="@id/btn_favorite"
            app:layout_constraintStart_toEndOf="@id/iv_channel_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_channel_group"
            tools:text="Now: Movie Title" />

        <!-- Favorite Button -->
        <ImageButton
            android:id="@+id/btn_favorite"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/cd_favorite_button"
            android:src="@drawable/ic_favorite_border"
            android:tint="@color/text_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Live Indicator -->
        <TextView
            android:id="@+id/tv_live_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/live_indicator_background"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:text="@string/live"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/iv_channel_logo"
            app:layout_constraintTop_toTopOf="@id/iv_channel_logo"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
