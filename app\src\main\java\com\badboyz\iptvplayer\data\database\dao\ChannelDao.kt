package com.badboyz.iptvplayer.data.database.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.badboyz.iptvplayer.data.model.Channel
import kotlinx.coroutines.flow.Flow

@Dao
interface ChannelDao {
    
    @Query("SELECT * FROM channels WHERE playlistId = :playlistId ORDER BY sortOrder ASC, name ASC")
    fun getChannelsByPlaylist(playlistId: Long): Flow<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE playlistId = :playlistId ORDER BY sortOrder ASC, name ASC")
    fun getChannelsByPlaylistLiveData(playlistId: Long): LiveData<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE isFavorite = 1 ORDER BY name ASC")
    fun getFavoriteChannels(): Flow<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE isFavorite = 1 ORDER BY name ASC")
    fun getFavoriteChannelsLiveData(): LiveData<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE name LIKE '%' || :query || '%' OR `group` LIKE '%' || :query || '%'")
    fun searchChannels(query: String): Flow<List<Channel>>
    
    @Query("SELECT DISTINCT `group` FROM channels WHERE playlistId = :playlistId AND `group` IS NOT NULL ORDER BY `group` ASC")
    fun getGroupsByPlaylist(playlistId: Long): Flow<List<String>>
    
    @Query("SELECT * FROM channels WHERE playlistId = :playlistId AND `group` = :group ORDER BY sortOrder ASC, name ASC")
    fun getChannelsByGroup(playlistId: Long, group: String): Flow<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE id = :channelId")
    suspend fun getChannelById(channelId: Long): Channel?
    
    @Query("SELECT * FROM channels ORDER BY lastWatched DESC LIMIT :limit")
    fun getRecentlyWatchedChannels(limit: Int = 10): Flow<List<Channel>>
    
    @Query("SELECT * FROM channels ORDER BY watchCount DESC LIMIT :limit")
    fun getMostWatchedChannels(limit: Int = 10): Flow<List<Channel>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChannel(channel: Channel): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChannels(channels: List<Channel>)
    
    @Update
    suspend fun updateChannel(channel: Channel)
    
    @Query("UPDATE channels SET isFavorite = :isFavorite WHERE id = :channelId")
    suspend fun updateFavoriteStatus(channelId: Long, isFavorite: Boolean)
    
    @Query("UPDATE channels SET lastWatched = :timestamp, watchCount = watchCount + 1 WHERE id = :channelId")
    suspend fun updateWatchHistory(channelId: Long, timestamp: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteChannel(channel: Channel)
    
    @Query("DELETE FROM channels WHERE playlistId = :playlistId")
    suspend fun deleteChannelsByPlaylist(playlistId: Long)
    
    @Query("DELETE FROM channels")
    suspend fun deleteAllChannels()
    
    @Query("SELECT COUNT(*) FROM channels WHERE playlistId = :playlistId")
    suspend fun getChannelCountByPlaylist(playlistId: Long): Int
}
