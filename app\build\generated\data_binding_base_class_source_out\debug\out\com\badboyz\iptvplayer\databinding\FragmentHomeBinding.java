// Generated by view binder compiler. Do not edit!
package com.badboyz.iptvplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.badboyz.iptvplayer.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final MaterialCardView cardAddPlaylist;

  @NonNull
  public final MaterialCardView cardFavorites;

  @NonNull
  public final RecyclerView rvPlaylists;

  @NonNull
  public final RecyclerView rvRecentlyWatched;

  private FragmentHomeBinding(@NonNull NestedScrollView rootView,
      @NonNull MaterialCardView cardAddPlaylist, @NonNull MaterialCardView cardFavorites,
      @NonNull RecyclerView rvPlaylists, @NonNull RecyclerView rvRecentlyWatched) {
    this.rootView = rootView;
    this.cardAddPlaylist = cardAddPlaylist;
    this.cardFavorites = cardFavorites;
    this.rvPlaylists = rvPlaylists;
    this.rvRecentlyWatched = rvRecentlyWatched;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.card_add_playlist;
      MaterialCardView cardAddPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (cardAddPlaylist == null) {
        break missingId;
      }

      id = R.id.card_favorites;
      MaterialCardView cardFavorites = ViewBindings.findChildViewById(rootView, id);
      if (cardFavorites == null) {
        break missingId;
      }

      id = R.id.rv_playlists;
      RecyclerView rvPlaylists = ViewBindings.findChildViewById(rootView, id);
      if (rvPlaylists == null) {
        break missingId;
      }

      id = R.id.rv_recently_watched;
      RecyclerView rvRecentlyWatched = ViewBindings.findChildViewById(rootView, id);
      if (rvRecentlyWatched == null) {
        break missingId;
      }

      return new FragmentHomeBinding((NestedScrollView) rootView, cardAddPlaylist, cardFavorites,
          rvPlaylists, rvRecentlyWatched);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
