@echo off
echo Installing BadBoyz IPTV Player...

REM Set JAVA_HOME to Android Studio's bundled JDK
set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
set ANDROID_SDK_ROOT=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%JAVA_HOME%\bin;%ANDROID_SDK_ROOT%\platform-tools;%PATH%

REM Check if APK exists
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo APK found! Installing to connected device/emulator...
    
    REM Check if ADB is available
    where adb >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo Installing APK...
        adb install -r "app\build\outputs\apk\debug\app-debug.apk"
        echo.
        echo Installation complete!
        echo You can now launch "BadBoyz IPTV Player" from your device.
    ) else (
        echo ADB not found in PATH. 
        echo Please install the APK manually from: app\build\outputs\apk\debug\app-debug.apk
    )
) else (
    echo APK not found. Please build the project first using build_app.bat
)

pause
