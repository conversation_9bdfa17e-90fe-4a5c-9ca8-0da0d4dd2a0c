{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\dcafc80b61e93bbcb116fecec747e0b5\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,949,1042,1147,1229,1327,1435,1513,1588,1679,1772,1867,1961,2061,2154,2249,2343,2434,2525,2603,2705,2803,2898,3001,3097,3193,3341,17428", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "944,1037,1142,1224,1322,1430,1508,1583,1674,1767,1862,1956,2056,2149,2244,2338,2429,2520,2598,2700,2798,2893,2996,3092,3188,3336,3433,17502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4759b026881da3a3aff1b5bbcb3454af\\transformed\\navigation-ui-2.7.5\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,109", "endOffsets": "149,259"}, "to": {"startLines": "220,221", "startColumns": "4,4", "startOffsets": "16629,16728", "endColumns": "98,109", "endOffsets": "16723,16833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ec96043c8b7c92d308a76e9418d0c48a\\transformed\\material-1.10.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,944,1028,1092,1150,1231,1292,1356,1411,1470,1527,1581,1674,1730,1787,1841,1907,2007,2083,2164,2286,2348,2410,2511,2590,2665,2718,2769,2835,2905,2975,3052,3122,3186,3257,3325,3388,3479,3558,3621,3701,3783,3855,3926,3998,4046,4118,4182,4257,4334,4396,4460,4523,4590,4676,4762,4843,4926,4983,5038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,100,78,74,52,50,65,69,69,76,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72", "endOffsets": "248,315,379,448,529,611,696,800,876,939,1023,1087,1145,1226,1287,1351,1406,1465,1522,1576,1669,1725,1782,1836,1902,2002,2078,2159,2281,2343,2405,2506,2585,2660,2713,2764,2830,2900,2970,3047,3117,3181,3252,3320,3383,3474,3553,3616,3696,3778,3850,3921,3993,4041,4113,4177,4252,4329,4391,4455,4518,4585,4671,4757,4838,4921,4978,5033,5106"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "699,3855,3922,3986,4055,4136,4887,4972,5076,8873,12379,12463,12683,12741,12822,12883,12947,13002,13061,13118,13172,13265,13321,13378,13432,13498,13598,13674,13755,13877,13939,14001,14102,14181,14256,14309,14360,14426,14496,14566,14643,14713,14777,14848,14916,14979,15070,15149,15212,15292,15374,15446,15517,15589,15637,15709,15773,15848,15925,15987,16051,16114,16181,16267,16353,16434,16517,16574,17126", "endLines": "22,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,100,78,74,52,50,65,69,69,76,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72", "endOffsets": "847,3917,3981,4050,4131,4213,4967,5071,5147,8931,12458,12522,12736,12817,12878,12942,12997,13056,13113,13167,13260,13316,13373,13427,13493,13593,13669,13750,13872,13934,13996,14097,14176,14251,14304,14355,14421,14491,14561,14638,14708,14772,14843,14911,14974,15065,15144,15207,15287,15369,15441,15512,15584,15632,15704,15768,15843,15920,15982,16046,16109,16176,16262,16348,16429,16512,16569,16624,17194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\55604ab945968ea65ece63dd7ad46a32\\transformed\\core-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "59,60,61,62,63,64,65,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4218,4310,4410,4504,4600,4693,4786,17655", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "4305,4405,4499,4595,4688,4781,4882,17751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a20a72c21acf2a21d60cd6f37a281562\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "69,70,103,104,106,162,163,222,223,224,225,227,228,231,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5152,5238,8533,8622,8790,12527,12605,16838,16923,16998,17062,17199,17273,17507,17849,17925,17990", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "5233,5311,8617,8714,8868,12600,12678,16918,16993,17057,17121,17268,17344,17571,17920,17985,18102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\98ebe932c34efba361490b6fec49f11d\\transformed\\media3-exoplayer-1.2.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,180,245,307,381,440,520,597", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "115,175,240,302,376,435,515,592,657"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10643,10708,10768,10833,10895,10969,11028,11108,11185", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "10703,10763,10828,10890,10964,11023,11103,11180,11245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efcfa4e78b3b22010a075bf53629cfd5\\transformed\\media3-ui-1.2.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,604,676,747,816,895,973,1039,1100,1178,1255,1319,1380,1439,1504,1591,1678,1766,1831,1897,1962,2026,2107,2187,2248,2311,2363,2421,2469,2530,2586,2648,2705,2765,2821,2877,2940,3002,3065,3115,3173,3245,3317", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,86,86,87,64,65,64,63,80,79,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "276,439,599,671,742,811,890,968,1034,1095,1173,1250,1314,1375,1434,1499,1586,1673,1761,1826,1892,1957,2021,2102,2182,2243,2306,2358,2416,2464,2525,2581,2643,2700,2760,2816,2872,2935,2997,3060,3110,3168,3240,3312,3361"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,376,539,8936,9008,9079,9148,9227,9305,9371,9432,9510,9587,9651,9712,9771,9836,9923,10010,10098,10163,10229,10294,10358,10439,10519,10580,11250,11302,11360,11408,11469,11525,11587,11644,11704,11760,11816,11879,11941,12004,12054,12112,12184,12256", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,86,86,87,64,65,64,63,80,79,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "371,534,694,9003,9074,9143,9222,9300,9366,9427,9505,9582,9646,9707,9766,9831,9918,10005,10093,10158,10224,10289,10353,10434,10514,10575,10638,11297,11355,11403,11464,11520,11582,11639,11699,11755,11811,11874,11936,11999,12049,12107,12179,12251,12300"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b89cf8e779a00d2af6b7386c87a67af\\transformed\\material3-1.1.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,265,370,472,547,633,737,852,960,1080,1158,1251,1332,1418,1521,1630,1728,1848,1966,2076,2199,2305,2402,2503,2606,2691,2782,2886,2994,3081,3174,3267,3384,3507,3602,3689,3760,3834,3913,3992,4085,4161,4239,4333,4424,4513,4606,4685,4777,4868,4961,5068,5144,5240", "endColumns": "106,102,104,101,74,85,103,114,107,119,77,92,80,85,102,108,97,119,117,109,122,105,96,100,102,84,90,103,107,86,92,92,116,122,94,86,70,73,78,78,92,75,77,93,90,88,92,78,91,90,92,106,75,95,89", "endOffsets": "157,260,365,467,542,628,732,847,955,1075,1153,1246,1327,1413,1516,1625,1723,1843,1961,2071,2194,2300,2397,2498,2601,2686,2777,2881,2989,3076,3169,3262,3379,3502,3597,3684,3755,3829,3908,3987,4080,4156,4234,4328,4419,4508,4601,4680,4772,4863,4956,5063,5139,5235,5325"}, "to": {"startLines": "50,51,52,53,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,105,159,229,232,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3438,3545,3648,3753,5316,5391,5477,5581,5696,5804,5924,6002,6095,6176,6262,6365,6474,6572,6692,6810,6920,7043,7149,7246,7347,7450,7535,7626,7730,7838,7925,8018,8111,8228,8351,8446,8719,12305,17349,17576,17756,18107,18183,18261,18355,18446,18535,18628,18707,18799,18890,18983,19090,19166,19262", "endColumns": "106,102,104,101,74,85,103,114,107,119,77,92,80,85,102,108,97,119,117,109,122,105,96,100,102,84,90,103,107,86,92,92,116,122,94,86,70,73,78,78,92,75,77,93,90,88,92,78,91,90,92,106,75,95,89", "endOffsets": "3540,3643,3748,3850,5386,5472,5576,5691,5799,5919,5997,6090,6171,6257,6360,6469,6567,6687,6805,6915,7038,7144,7241,7342,7445,7530,7621,7725,7833,7920,8013,8106,8223,8346,8441,8528,8785,12374,17423,17650,17844,18178,18256,18350,18441,18530,18623,18702,18794,18885,18978,19085,19161,19257,19347"}}]}]}