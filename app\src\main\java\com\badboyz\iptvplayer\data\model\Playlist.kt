package com.badboyz.iptvplayer.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "playlists")
data class Playlist(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val url: String,
    val isLocal: Boolean = false,
    val filePath: String? = null,
    val lastUpdated: Long = System.currentTimeMillis(),
    val channelCount: Int = 0,
    val isActive: Boolean = true,
    val userAgent: String? = null,
    val referer: String? = null,
    val headers: String? = null,
    val autoRefresh: Boolean = false,
    val refreshInterval: Long = 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    val lastRefresh: Long = 0,
    val sortOrder: Int = 0
) : Parcelable
