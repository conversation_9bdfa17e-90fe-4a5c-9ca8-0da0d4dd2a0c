// Generated by view binder compiler. Do not edit!
package com.badboyz.iptvplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.badboyz.iptvplayer.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPlaylistBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnPlaylistMenu;

  @NonNull
  public final ImageView ivPlaylistIcon;

  @NonNull
  public final View statusIndicator;

  @NonNull
  public final TextView tvChannelCount;

  @NonNull
  public final TextView tvLastUpdated;

  @NonNull
  public final TextView tvPlaylistName;

  private ItemPlaylistBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton btnPlaylistMenu, @NonNull ImageView ivPlaylistIcon,
      @NonNull View statusIndicator, @NonNull TextView tvChannelCount,
      @NonNull TextView tvLastUpdated, @NonNull TextView tvPlaylistName) {
    this.rootView = rootView;
    this.btnPlaylistMenu = btnPlaylistMenu;
    this.ivPlaylistIcon = ivPlaylistIcon;
    this.statusIndicator = statusIndicator;
    this.tvChannelCount = tvChannelCount;
    this.tvLastUpdated = tvLastUpdated;
    this.tvPlaylistName = tvPlaylistName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPlaylistBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPlaylistBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_playlist, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPlaylistBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_playlist_menu;
      ImageButton btnPlaylistMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnPlaylistMenu == null) {
        break missingId;
      }

      id = R.id.iv_playlist_icon;
      ImageView ivPlaylistIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivPlaylistIcon == null) {
        break missingId;
      }

      id = R.id.status_indicator;
      View statusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicator == null) {
        break missingId;
      }

      id = R.id.tv_channel_count;
      TextView tvChannelCount = ViewBindings.findChildViewById(rootView, id);
      if (tvChannelCount == null) {
        break missingId;
      }

      id = R.id.tv_last_updated;
      TextView tvLastUpdated = ViewBindings.findChildViewById(rootView, id);
      if (tvLastUpdated == null) {
        break missingId;
      }

      id = R.id.tv_playlist_name;
      TextView tvPlaylistName = ViewBindings.findChildViewById(rootView, id);
      if (tvPlaylistName == null) {
        break missingId;
      }

      return new ItemPlaylistBinding((MaterialCardView) rootView, btnPlaylistMenu, ivPlaylistIcon,
          statusIndicator, tvChannelCount, tvLastUpdated, tvPlaylistName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
