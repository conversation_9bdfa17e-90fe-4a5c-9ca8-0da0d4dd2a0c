<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background_selected" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/primary" />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background_selected" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/card_stroke" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/card_stroke" />
        </shape>
    </item>
</selector>
