{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\dcafc80b61e93bbcb116fecec747e0b5\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "907,1014,1116,1225,1309,1412,1531,1609,1685,1776,1869,1964,2058,2158,2251,2346,2440,2531,2622,2706,2810,2918,3019,3124,3239,3344,3501,19111", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "1009,1111,1220,1304,1407,1526,1604,1680,1771,1864,1959,2053,2153,2246,2341,2435,2526,2617,2701,2805,2913,3014,3119,3234,3339,3496,3595,19191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4759b026881da3a3aff1b5bbcb3454af\\transformed\\navigation-ui-2.7.5\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,112", "endOffsets": "158,271"}, "to": {"startLines": "220,221", "startColumns": "4,4", "startOffsets": "18231,18339", "endColumns": "107,112", "endOffsets": "18334,18447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efcfa4e78b3b22010a075bf53629cfd5\\transformed\\media3-ui-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,473,647,723,798,870,973,1074,1153,1221,1320,1421,1489,1552,1615,1683,1813,1933,2060,2128,2206,2276,2361,2446,2530,2593,2667,2720,2781,2831,2892,2954,3020,3084,3149,3210,3269,3338,3405,3471,3529,3589,3663,3737", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,66,65,57,59,73,73,62", "endOffsets": "285,468,642,718,793,865,968,1069,1148,1216,1315,1416,1484,1547,1610,1678,1808,1928,2055,2123,2201,2271,2356,2441,2525,2588,2662,2715,2776,2826,2887,2949,3015,3079,3144,3205,3264,3333,3400,3466,3524,3584,3658,3732,3795"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,385,568,9593,9669,9744,9816,9919,10020,10099,10167,10266,10367,10435,10498,10561,10629,10759,10879,11006,11074,11152,11222,11307,11392,11476,11539,12305,12358,12419,12469,12530,12592,12658,12722,12787,12848,12907,12976,13043,13109,13167,13227,13301,13375", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,66,65,57,59,73,73,62", "endOffsets": "380,563,737,9664,9739,9811,9914,10015,10094,10162,10261,10362,10430,10493,10556,10624,10754,10874,11001,11069,11147,11217,11302,11387,11471,11534,11608,12353,12414,12464,12525,12587,12653,12717,12782,12843,12902,12971,13038,13104,13162,13222,13296,13370,13433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\55604ab945968ea65ece63dd7ad46a32\\transformed\\core-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "59,60,61,62,63,64,65,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4438,4535,4637,4736,4836,4939,5052,19348", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "4530,4632,4731,4831,4934,5047,5163,19444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b89cf8e779a00d2af6b7386c87a67af\\transformed\\material3-1.1.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,386,499,578,669,778,911,1027,1164,1244,1343,1428,1520,1635,1754,1858,1981,2100,2223,2378,2501,2620,2737,2853,2939,3035,3149,3278,3369,3471,3574,3692,3818,3922,4013,4088,4166,4251,4331,4434,4510,4589,4684,4778,4869,4965,5047,5144,5238,5336,5448,5524,5633", "endColumns": "114,112,102,112,78,90,108,132,115,136,79,98,84,91,114,118,103,122,118,122,154,122,118,116,115,85,95,113,128,90,101,102,117,125,103,90,74,77,84,79,102,75,78,94,93,90,95,81,96,93,97,111,75,108,99", "endOffsets": "165,278,381,494,573,664,773,906,1022,1159,1239,1338,1423,1515,1630,1749,1853,1976,2095,2218,2373,2496,2615,2732,2848,2934,3030,3144,3273,3364,3466,3569,3687,3813,3917,4008,4083,4161,4246,4326,4429,4505,4584,4679,4773,4864,4960,5042,5139,5233,5331,5443,5519,5628,5728"}, "to": {"startLines": "50,51,52,53,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,105,159,229,232,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3600,3715,3828,3931,5643,5722,5813,5922,6055,6171,6308,6388,6487,6572,6664,6779,6898,7002,7125,7244,7367,7522,7645,7764,7881,7997,8083,8179,8293,8422,8513,8615,8718,8836,8962,9066,9363,13438,19026,19268,19449,19809,19885,19964,20059,20153,20244,20340,20422,20519,20613,20711,20823,20899,21008", "endColumns": "114,112,102,112,78,90,108,132,115,136,79,98,84,91,114,118,103,122,118,122,154,122,118,116,115,85,95,113,128,90,101,102,117,125,103,90,74,77,84,79,102,75,78,94,93,90,95,81,96,93,97,111,75,108,99", "endOffsets": "3710,3823,3926,4039,5717,5808,5917,6050,6166,6303,6383,6482,6567,6659,6774,6893,6997,7120,7239,7362,7517,7640,7759,7876,7992,8078,8174,8288,8417,8508,8610,8713,8831,8957,9061,9152,9433,13511,19106,19343,19547,19880,19959,20054,20148,20239,20335,20417,20514,20608,20706,20818,20894,21003,21103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a20a72c21acf2a21d60cd6f37a281562\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "69,70,103,104,106,162,163,222,223,224,225,227,228,231,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5461,5557,9157,9263,9438,13684,13769,18452,18546,18627,18697,18845,18935,19196,19552,19629,19695", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "5552,5638,9258,9358,9525,13764,13857,18541,18622,18692,18762,18930,19021,19263,19624,19690,19804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\98ebe932c34efba361490b6fec49f11d\\transformed\\media3-exoplayer-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,259,328,419,489,579,667", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "123,186,254,323,414,484,574,662,742"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11613,11686,11749,11817,11886,11977,12047,12137,12225", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "11681,11744,11812,11881,11972,12042,12132,12220,12300"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ec96043c8b7c92d308a76e9418d0c48a\\transformed\\material-1.10.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1109,1183,1242,1328,1390,1451,1509,1573,1634,1688,1805,1862,1922,1976,2051,2178,2262,2340,2470,2554,2632,2766,2857,2938,2989,3040,3106,3174,3250,3331,3411,3490,3565,3638,3714,3820,3909,3986,4077,4171,4245,4315,4408,4457,4538,4604,4689,4775,4837,4901,4964,5035,5134,5239,5337,5442,5497,5552", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,133,90,80,50,50,65,67,75,80,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1104,1178,1237,1323,1385,1446,1504,1568,1629,1683,1800,1857,1917,1971,2046,2173,2257,2335,2465,2549,2627,2761,2852,2933,2984,3035,3101,3169,3245,3326,3406,3485,3560,3633,3709,3815,3904,3981,4072,4166,4240,4310,4403,4452,4533,4599,4684,4770,4832,4896,4959,5030,5129,5234,5332,5437,5492,5547,5625"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,4044,4123,4200,4278,4358,5168,5267,5381,9530,13516,13610,13862,13921,14007,14069,14130,14188,14252,14313,14367,14484,14541,14601,14655,14730,14857,14941,15019,15149,15233,15311,15445,15536,15617,15668,15719,15785,15853,15929,16010,16090,16169,16244,16317,16393,16499,16588,16665,16756,16850,16924,16994,17087,17136,17217,17283,17368,17454,17516,17580,17643,17714,17813,17918,18016,18121,18176,18767", "endLines": "22,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,133,90,80,50,50,65,67,75,80,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77", "endOffsets": "902,4118,4195,4273,4353,4433,5262,5376,5456,9588,13605,13679,13916,14002,14064,14125,14183,14247,14308,14362,14479,14536,14596,14650,14725,14852,14936,15014,15144,15228,15306,15440,15531,15612,15663,15714,15780,15848,15924,16005,16085,16164,16239,16312,16388,16494,16583,16660,16751,16845,16919,16989,17082,17131,17212,17278,17363,17449,17511,17575,17638,17709,17808,17913,18011,18116,18171,18226,18840"}}]}]}