{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8c441e88d76142b996bad90a67757093\\transformed\\exoplayer-ui-2.19.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,655,737,817,894,982,1064,1140,1204,1297,1389,1459,1523,1586,1656,1766,1873,1983,2051,2128,2198,2274,2358,2440,2502,2565,2618,2676,2724,2785,2844,2912,2973,3039,3103,3162,3226,3293,3360,3414,3474,3548,3622", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,732,812,889,977,1059,1135,1199,1292,1384,1454,1518,1581,1651,1761,1868,1978,2046,2123,2193,2269,2353,2435,2497,2560,2613,2671,2719,2780,2839,2907,2968,3034,3098,3157,3221,3288,3355,3409,3469,3543,3617,3673"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,572,9495,9577,9657,9734,9822,9904,9980,10044,10137,10229,10299,10363,10426,10496,10606,10713,10823,10891,10968,11038,11114,11198,11280,11342,12062,12115,12173,12221,12282,12341,12409,12470,12536,12600,12659,12723,12790,12857,12911,12971,13045,13119", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "376,567,745,9572,9652,9729,9817,9899,9975,10039,10132,10224,10294,10358,10421,10491,10601,10708,10818,10886,10963,11033,11109,11193,11275,11337,11400,12110,12168,12216,12277,12336,12404,12465,12531,12595,12654,12718,12785,12852,12906,12966,13040,13114,13170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c30a6e1576391bb1585dd679e4d5216\\transformed\\navigation-ui-2.7.5\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,109", "endOffsets": "159,269"}, "to": {"startLines": "220,221", "startColumns": "4,4", "startOffsets": "17934,18043", "endColumns": "108,109", "endOffsets": "18038,18148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5c9ad5896dbe4b4ab7793f66459dd36f\\transformed\\exoplayer-core-2.19.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11405,11474,11535,11601,11666,11741,11811,11903,11990", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "11469,11530,11596,11661,11736,11806,11898,11985,12057"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bf78663ddf7e5a17052df8a5e558236f\\transformed\\material3-1.1.2\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,379,486,563,655,765,890,1004,1131,1212,1308,1394,1490,1604,1720,1821,1944,2065,2191,2337,2454,2564,2679,2788,2875,2970,3079,3200,3290,3389,3491,3613,3743,3849,3941,4017,4095,4177,4259,4359,4441,4524,4623,4721,4812,4911,4993,5090,5184,5281,5404,5486,5582", "endColumns": "109,107,105,106,76,91,109,124,113,126,80,95,85,95,113,115,100,122,120,125,145,116,109,114,108,86,94,108,120,89,98,101,121,129,105,91,75,77,81,81,99,81,82,98,97,90,98,81,96,93,96,122,81,95,90", "endOffsets": "160,268,374,481,558,650,760,885,999,1126,1207,1303,1389,1485,1599,1715,1816,1939,2060,2186,2332,2449,2559,2674,2783,2870,2965,3074,3195,3285,3384,3486,3608,3738,3844,3936,4012,4090,4172,4254,4354,4436,4519,4618,4716,4807,4906,4988,5085,5179,5276,5399,5481,5577,5668"}, "to": {"startLines": "50,51,52,53,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,105,159,229,232,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3586,3696,3804,3910,5602,5679,5771,5881,6006,6120,6247,6328,6424,6510,6606,6720,6836,6937,7060,7181,7307,7453,7570,7680,7795,7904,7991,8086,8195,8316,8406,8505,8607,8729,8859,8965,9264,13175,18696,18934,19117,19482,19564,19647,19746,19844,19935,20034,20116,20213,20307,20404,20527,20609,20705", "endColumns": "109,107,105,106,76,91,109,124,113,126,80,95,85,95,113,115,100,122,120,125,145,116,109,114,108,86,94,108,120,89,98,101,121,129,105,91,75,77,81,81,99,81,82,98,97,90,98,81,96,93,96,122,81,95,90", "endOffsets": "3691,3799,3905,4012,5674,5766,5876,6001,6115,6242,6323,6419,6505,6601,6715,6831,6932,7055,7176,7302,7448,7565,7675,7790,7899,7986,8081,8190,8311,8401,8500,8602,8724,8854,8960,9052,9335,13248,18773,19011,19212,19559,19642,19741,19839,19930,20029,20111,20208,20302,20399,20522,20604,20700,20791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c711e708460550b0e6d2719a43fe6f2e\\transformed\\material-1.10.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1047,1136,1205,1264,1359,1425,1490,1548,1613,1674,1734,1840,1901,1961,2019,2090,2209,2295,2377,2520,2595,2671,2802,2892,2970,3025,3080,3146,3215,3289,3368,3447,3520,3597,3666,3736,3833,3918,3993,4086,4179,4253,4322,4416,4468,4551,4618,4702,4786,4848,4912,4975,5045,5144,5242,5337,5431,5490,5549", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "264,338,409,490,576,659,774,893,976,1042,1131,1200,1259,1354,1420,1485,1543,1608,1669,1729,1835,1896,1956,2014,2085,2204,2290,2372,2515,2590,2666,2797,2887,2965,3020,3075,3141,3210,3284,3363,3442,3515,3592,3661,3731,3828,3913,3988,4081,4174,4248,4317,4411,4463,4546,4613,4697,4781,4843,4907,4970,5040,5139,5237,5332,5426,5485,5544,5623"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "750,4017,4091,4162,4243,4329,5118,5233,5352,9429,13253,13342,13590,13649,13744,13810,13875,13933,13998,14059,14119,14225,14286,14346,14404,14475,14594,14680,14762,14905,14980,15056,15187,15277,15355,15410,15465,15531,15600,15674,15753,15832,15905,15982,16051,16121,16218,16303,16378,16471,16564,16638,16707,16801,16853,16936,17003,17087,17171,17233,17297,17360,17430,17529,17627,17722,17816,17875,18453", "endLines": "22,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "914,4086,4157,4238,4324,4407,5228,5347,5430,9490,13337,13406,13644,13739,13805,13870,13928,13993,14054,14114,14220,14281,14341,14399,14470,14589,14675,14757,14900,14975,15051,15182,15272,15350,15405,15460,15526,15595,15669,15748,15827,15900,15977,16046,16116,16213,16298,16373,16466,16559,16633,16702,16796,16848,16931,16998,17082,17166,17228,17292,17355,17425,17524,17622,17717,17811,17870,17929,18527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7eb203db16c40ca18c3e77706606711f\\transformed\\core-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "59,60,61,62,63,64,65,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4412,4508,4611,4710,4808,4909,5007,19016", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "4503,4606,4705,4803,4904,5002,5113,19112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\3f6383dc1c59a74ffea8dbe469a360a8\\transformed\\appcompat-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "919,1022,1125,1238,1323,1427,1538,1616,1693,1784,1877,1969,2063,2163,2256,2351,2447,2538,2629,2710,2817,2921,3019,3122,3226,3330,3487,18778", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "1017,1120,1233,1318,1422,1533,1611,1688,1779,1872,1964,2058,2158,2251,2346,2442,2533,2624,2705,2812,2916,3014,3117,3221,3325,3482,3581,18855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efe0e3583596feeea587a194dc746a51\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "69,70,103,104,106,162,163,222,223,224,225,227,228,231,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5435,5525,9057,9166,9340,13411,13500,18153,18239,18322,18387,18532,18612,18860,19217,19295,19361", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "5520,5597,9161,9259,9424,13495,13585,18234,18317,18382,18448,18607,18691,18929,19290,19356,19477"}}]}]}