package com.badboyz.iptvplayer.ui.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.badboyz.iptvplayer.data.model.Playlist
import com.badboyz.iptvplayer.databinding.ItemPlaylistBinding
import java.text.SimpleDateFormat
import java.util.*

class PlaylistAdapter(
    private val onPlaylistClick: (Playlist) -> Unit,
    private val onPlaylistMenuClick: (Playlist) -> Unit
) : ListAdapter<Playlist, PlaylistAdapter.PlaylistViewHolder>(PlaylistDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PlaylistViewHolder {
        val binding = ItemPlaylistBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PlaylistViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: PlaylistViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class PlaylistViewHolder(
        private val binding: ItemPlaylistBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(playlist: Playlist) {
            binding.apply {
                tvPlaylistName.text = playlist.name
                tvChannelCount.text = "${playlist.channelCount} channels"
                
                // Format last updated time
                val dateFormat = SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault())
                val lastUpdated = dateFormat.format(Date(playlist.lastUpdated))
                tvLastUpdated.text = "Updated $lastUpdated"
                
                // Set status indicator visibility
                statusIndicator.visibility = if (playlist.isActive) {
                    android.view.View.VISIBLE
                } else {
                    android.view.View.GONE
                }
                
                // Set click listeners
                root.setOnClickListener {
                    onPlaylistClick(playlist)
                }
                
                btnPlaylistMenu.setOnClickListener {
                    onPlaylistMenuClick(playlist)
                }
            }
        }
    }
    
    private class PlaylistDiffCallback : DiffUtil.ItemCallback<Playlist>() {
        override fun areItemsTheSame(oldItem: Playlist, newItem: Playlist): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: Playlist, newItem: Playlist): Boolean {
            return oldItem == newItem
        }
    }
}
