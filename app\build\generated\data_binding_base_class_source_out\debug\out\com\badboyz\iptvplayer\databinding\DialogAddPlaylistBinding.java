// Generated by view binder compiler. Do not edit!
package com.badboyz.iptvplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.badboyz.iptvplayer.R;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddPlaylistBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText etPlaylistName;

  @NonNull
  public final TextInputEditText etPlaylistUrl;

  @NonNull
  public final TextInputLayout tilPlaylistName;

  @NonNull
  public final TextInputLayout tilPlaylistUrl;

  private DialogAddPlaylistBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText etPlaylistName, @NonNull TextInputEditText etPlaylistUrl,
      @NonNull TextInputLayout tilPlaylistName, @NonNull TextInputLayout tilPlaylistUrl) {
    this.rootView = rootView;
    this.etPlaylistName = etPlaylistName;
    this.etPlaylistUrl = etPlaylistUrl;
    this.tilPlaylistName = tilPlaylistName;
    this.tilPlaylistUrl = tilPlaylistUrl;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddPlaylistBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddPlaylistBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_playlist, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddPlaylistBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_playlist_name;
      TextInputEditText etPlaylistName = ViewBindings.findChildViewById(rootView, id);
      if (etPlaylistName == null) {
        break missingId;
      }

      id = R.id.et_playlist_url;
      TextInputEditText etPlaylistUrl = ViewBindings.findChildViewById(rootView, id);
      if (etPlaylistUrl == null) {
        break missingId;
      }

      id = R.id.til_playlist_name;
      TextInputLayout tilPlaylistName = ViewBindings.findChildViewById(rootView, id);
      if (tilPlaylistName == null) {
        break missingId;
      }

      id = R.id.til_playlist_url;
      TextInputLayout tilPlaylistUrl = ViewBindings.findChildViewById(rootView, id);
      if (tilPlaylistUrl == null) {
        break missingId;
      }

      return new DialogAddPlaylistBinding((LinearLayout) rootView, etPlaylistName, etPlaylistUrl,
          tilPlaylistName, tilPlaylistUrl);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
