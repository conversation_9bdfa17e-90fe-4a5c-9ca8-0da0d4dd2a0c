package com.badboyz.iptvplayer.ui.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.badboyz.iptvplayer.R
import com.badboyz.iptvplayer.data.model.Channel
import com.badboyz.iptvplayer.databinding.ItemChannelBinding
import com.badboyz.iptvplayer.databinding.ItemChannelHorizontalBinding
import com.bumptech.glide.Glide

class ChannelAdapter(
    private val onChannelClick: (Channel) -> Unit,
    private val onFavoriteClick: (Channel) -> Unit,
    private val isHorizontal: Boolean = false
) : ListAdapter<Channel, RecyclerView.ViewHolder>(ChannelDiffCallback()) {
    
    companion object {
        private const val TYPE_VERTICAL = 0
        private const val TYPE_HORIZONTAL = 1
    }
    
    override fun getItemViewType(position: Int): Int {
        return if (isHorizontal) TYPE_HORIZONTAL else TYPE_VERTICAL
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_HORIZONTAL -> {
                val binding = ItemChannelHorizontalBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                HorizontalChannelViewHolder(binding)
            }
            else -> {
                val binding = ItemChannelBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                VerticalChannelViewHolder(binding)
            }
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val channel = getItem(position)
        when (holder) {
            is VerticalChannelViewHolder -> holder.bind(channel)
            is HorizontalChannelViewHolder -> holder.bind(channel)
        }
    }
    
    inner class VerticalChannelViewHolder(
        private val binding: ItemChannelBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(channel: Channel) {
            binding.apply {
                tvChannelName.text = channel.getDisplayName()
                tvChannelGroup.text = channel.getDisplayGroup()
                
                // Load channel logo
                val logoUrl = channel.getDisplayLogo()
                if (!logoUrl.isNullOrEmpty()) {
                    Glide.with(ivChannelLogo.context)
                        .load(logoUrl)
                        .placeholder(R.drawable.ic_tv)
                        .error(R.drawable.ic_tv)
                        .into(ivChannelLogo)
                } else {
                    ivChannelLogo.setImageResource(R.drawable.ic_tv)
                }
                
                // Set favorite button state
                val favoriteIcon = if (channel.isFavorite) {
                    R.drawable.ic_favorite
                } else {
                    R.drawable.ic_favorite_border
                }
                btnFavorite.setImageResource(favoriteIcon)
                
                // Show/hide live indicator
                tvLiveIndicator.visibility = View.GONE // TODO: Implement live detection
                
                // Show now playing if available
                tvNowPlaying.visibility = View.GONE // TODO: Implement EPG integration
                
                // Set click listeners
                root.setOnClickListener {
                    onChannelClick(channel)
                }
                
                btnFavorite.setOnClickListener {
                    onFavoriteClick(channel)
                }
            }
        }
    }
    
    inner class HorizontalChannelViewHolder(
        private val binding: ItemChannelHorizontalBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(channel: Channel) {
            binding.apply {
                tvChannelName.text = channel.getDisplayName()
                tvChannelGroup.text = channel.getDisplayGroup()
                
                // Load channel logo
                val logoUrl = channel.getDisplayLogo()
                if (!logoUrl.isNullOrEmpty()) {
                    Glide.with(ivChannelLogo.context)
                        .load(logoUrl)
                        .placeholder(R.drawable.ic_tv)
                        .error(R.drawable.ic_tv)
                        .into(ivChannelLogo)
                } else {
                    ivChannelLogo.setImageResource(R.drawable.ic_tv)
                }
                
                // Set favorite button state
                val favoriteIcon = if (channel.isFavorite) {
                    R.drawable.ic_favorite
                } else {
                    R.drawable.ic_favorite_border
                }
                btnFavorite.setImageResource(favoriteIcon)
                
                // Show/hide live indicator
                tvLiveIndicator.visibility = View.GONE // TODO: Implement live detection
                
                // Set click listeners
                root.setOnClickListener {
                    onChannelClick(channel)
                }
                
                btnFavorite.setOnClickListener {
                    onFavoriteClick(channel)
                }
            }
        }
    }
    
    private class ChannelDiffCallback : DiffUtil.ItemCallback<Channel>() {
        override fun areItemsTheSame(oldItem: Channel, newItem: Channel): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: Channel, newItem: Channel): Boolean {
            return oldItem == newItem
        }
    }
}
