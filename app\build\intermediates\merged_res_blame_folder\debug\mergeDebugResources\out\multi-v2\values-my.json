{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ec96043c8b7c92d308a76e9418d0c48a\\transformed\\material-1.10.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1121,1211,1286,1345,1436,1499,1564,1623,1694,1756,1813,1932,1990,2051,2106,2179,2311,2402,2491,2632,2710,2787,2910,3002,3079,3137,3188,3254,3326,3408,3490,3568,3643,3717,3789,3868,3976,4073,4154,4240,4332,4406,4485,4571,4625,4701,4769,4852,4933,4995,5059,5122,5190,5302,5413,5517,5630,5691,5746", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,122,91,76,57,50,65,71,81,81,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81", "endOffsets": "280,381,480,556,647,731,837,966,1051,1116,1206,1281,1340,1431,1494,1559,1618,1689,1751,1808,1927,1985,2046,2101,2174,2306,2397,2486,2627,2705,2782,2905,2997,3074,3132,3183,3249,3321,3403,3485,3563,3638,3712,3784,3863,3971,4068,4149,4235,4327,4401,4480,4566,4620,4696,4764,4847,4928,4990,5054,5117,5185,5297,5408,5512,5625,5686,5741,5823"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,4228,4329,4428,4504,4595,5421,5527,5656,9925,13931,14021,14279,14338,14429,14492,14557,14616,14687,14749,14806,14925,14983,15044,15099,15172,15304,15395,15484,15625,15703,15780,15903,15995,16072,16130,16181,16247,16319,16401,16483,16561,16636,16710,16782,16861,16969,17066,17147,17233,17325,17399,17478,17564,17618,17694,17762,17845,17926,17988,18052,18115,18183,18295,18406,18510,18623,18684,19282", "endLines": "22,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,122,91,76,57,50,65,71,81,81,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81", "endOffsets": "962,4324,4423,4499,4590,4674,5522,5651,5736,9985,14016,14091,14333,14424,14487,14552,14611,14682,14744,14801,14920,14978,15039,15094,15167,15299,15390,15479,15620,15698,15775,15898,15990,16067,16125,16176,16242,16314,16396,16478,16556,16631,16705,16777,16856,16964,17061,17142,17228,17320,17394,17473,17559,17613,17689,17757,17840,17921,17983,18047,18110,18178,18290,18401,18505,18618,18679,18734,19359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\55604ab945968ea65ece63dd7ad46a32\\transformed\\core-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "59,60,61,62,63,64,65,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4679,4782,4886,4989,5091,5196,5302,19866", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "4777,4881,4984,5086,5191,5297,5416,19962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b89cf8e779a00d2af6b7386c87a67af\\transformed\\material3-1.1.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,188,318,425,555,635,731,845,990,1110,1269,1351,1446,1535,1633,1749,1872,1972,2094,2221,2361,2525,2643,2756,2872,2996,3086,3179,3306,3439,3537,3645,3746,3867,3992,4091,4189,4266,4344,4430,4512,4624,4700,4780,4876,4974,5066,5160,5243,5344,5439,5535,5652,5728,5847", "endColumns": "132,129,106,129,79,95,113,144,119,158,81,94,88,97,115,122,99,121,126,139,163,117,112,115,123,89,92,126,132,97,107,100,120,124,98,97,76,77,85,81,111,75,79,95,97,91,93,82,100,94,95,116,75,118,113", "endOffsets": "183,313,420,550,630,726,840,985,1105,1264,1346,1441,1530,1628,1744,1867,1967,2089,2216,2356,2520,2638,2751,2867,2991,3081,3174,3301,3434,3532,3640,3741,3862,3987,4086,4184,4261,4339,4425,4507,4619,4695,4775,4871,4969,5061,5155,5238,5339,5434,5530,5647,5723,5842,5956"}, "to": {"startLines": "50,51,52,53,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,105,159,229,232,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3728,3861,3991,4098,5923,6003,6099,6213,6358,6478,6637,6719,6814,6903,7001,7117,7240,7340,7462,7589,7729,7893,8011,8124,8240,8364,8454,8547,8674,8807,8905,9013,9114,9235,9360,9459,9765,13853,19537,19784,19967,20352,20428,20508,20604,20702,20794,20888,20971,21072,21167,21263,21380,21456,21575", "endColumns": "132,129,106,129,79,95,113,144,119,158,81,94,88,97,115,122,99,121,126,139,163,117,112,115,123,89,92,126,132,97,107,100,120,124,98,97,76,77,85,81,111,75,79,95,97,91,93,82,100,94,95,116,75,118,113", "endOffsets": "3856,3986,4093,4223,5998,6094,6208,6353,6473,6632,6714,6809,6898,6996,7112,7235,7335,7457,7584,7724,7888,8006,8119,8235,8359,8449,8542,8669,8802,8900,9008,9109,9230,9355,9454,9552,9837,13926,19618,19861,20074,20423,20503,20599,20697,20789,20883,20966,21067,21162,21258,21375,21451,21570,21684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\98ebe932c34efba361490b6fec49f11d\\transformed\\media3-exoplayer-1.2.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,275,344,426,501,602,697", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "125,197,270,339,421,496,597,692,770"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12007,12082,12154,12227,12296,12378,12453,12554,12649", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "12077,12149,12222,12291,12373,12448,12549,12644,12722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4759b026881da3a3aff1b5bbcb3454af\\transformed\\navigation-ui-2.7.5\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,125", "endOffsets": "163,289"}, "to": {"startLines": "220,221", "startColumns": "4,4", "startOffsets": "18739,18852", "endColumns": "112,125", "endOffsets": "18847,18973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efcfa4e78b3b22010a075bf53629cfd5\\transformed\\media3-ui-1.2.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,292,491,687,771,853,940,1042,1138,1211,1278,1377,1472,1540,1607,1674,1741,1864,1985,2106,2177,2257,2330,2401,2489,2575,2640,2704,2757,2815,2865,2926,2984,3046,3119,3188,3253,3311,3375,3440,3508,3562,3624,3700,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,122,120,120,70,79,72,70,87,85,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "287,486,682,766,848,935,1037,1133,1206,1273,1372,1467,1535,1602,1669,1736,1859,1980,2101,2172,2252,2325,2396,2484,2570,2635,2699,2752,2810,2860,2921,2979,3041,3114,3183,3248,3306,3370,3435,3503,3557,3619,3695,3771,3825"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,387,586,9990,10074,10156,10243,10345,10441,10514,10581,10680,10775,10843,10910,10977,11044,11167,11288,11409,11480,11560,11633,11704,11792,11878,11943,12727,12780,12838,12888,12949,13007,13069,13142,13211,13276,13334,13398,13463,13531,13585,13647,13723,13799", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,122,120,120,70,79,72,70,87,85,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "382,581,777,10069,10151,10238,10340,10436,10509,10576,10675,10770,10838,10905,10972,11039,11162,11283,11404,11475,11555,11628,11699,11787,11873,11938,12002,12775,12833,12883,12944,13002,13064,13137,13206,13271,13329,13393,13458,13526,13580,13642,13718,13794,13848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\dcafc80b61e93bbcb116fecec747e0b5\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "967,1080,1187,1303,1390,1499,1622,1701,1779,1870,1963,2058,2152,2252,2345,2440,2534,2625,2716,2801,2916,3025,3124,3250,3357,3465,3625,19623", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "1075,1182,1298,1385,1494,1617,1696,1774,1865,1958,2053,2147,2247,2340,2435,2529,2620,2711,2796,2911,3020,3119,3245,3352,3460,3620,3723,19704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a20a72c21acf2a21d60cd6f37a281562\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,998,1065,1152,1238,1313,1394,1460", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,993,1060,1147,1233,1308,1389,1455,1581"}, "to": {"startLines": "69,70,103,104,106,162,163,222,223,224,225,227,228,231,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5741,5835,9557,9661,9842,14096,14180,18978,19067,19149,19215,19364,19451,19709,20079,20160,20226", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "5830,5918,9656,9760,9920,14175,14274,19062,19144,19210,19277,19446,19532,19779,20155,20221,20347"}}]}]}