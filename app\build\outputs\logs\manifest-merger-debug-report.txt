-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:1-75:12
MERGED from [androidx.databinding:databinding-adapters:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f005559fa46d9d9f267bc6dc21b6f74a\transformed\databinding-adapters-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\a84b45771e0eb8c803e9f6b7e1ba65f7\transformed\databinding-ktx-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8c0f19a396f58f0891f175b9b10c21c1\transformed\databinding-runtime-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\7921d919f8b5092c5acfbfa4bf05e791\transformed\viewbinding-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\7f60846c1ae9e86d0e4a59bf7c8de070\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\49b6ee58b8c1000a05168da9ce4f8212\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\289eef689cd649c2f0fe72dfdb28bce4\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\64d5d5a1489960e9e0388ea585384201\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\3941269e0a272f94f270edb2b4a55440\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\352ab48c38c648a8a5d49c36bbab4b79\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\42579f1f4a796df696b1d64b7ad2a0c2\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\4759b026881da3a3aff1b5bbcb3454af\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec96043c8b7c92d308a76e9418d0c48a\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3aa0627e5148a63948b207e42496341e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb321b3db659c06706ae4e76d9f2d26\transformed\lottie-6.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2b00f4d78e206fdb7d67bddbe7f714b7\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\dcafc80b61e93bbcb116fecec747e0b5\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b89cf8e779a00d2af6b7386c87a67af\transformed\material3-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\b5b0f4f13ed49f48ce7a093f200f4ebd\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\185df9d2eb29b059f6dfbcd869d39a86\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c86fa7cfe92505d5e17180c34c40adbc\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c963dd3299b7f63d478d26118d5d649b\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\aed7e083c287157aafa2ed63e56f741f\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6f7bf611fd22f9cf1dc5d6d69e9e1d4\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac122f2b9edcb317f3f8ea7fd600df67\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8bf1e03e740445a3b0bce7c5b8eef56c\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\046015eaa4de80232fad8306c7a50ce6\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8e3fdc2940e77d62dd61bcb70032b5a9\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7c74f49a41b480ba85efd06127adef\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c24d876e0a38ed7b6c94aba6260f347\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4a0cc5e8bed9707a9c3275f89240bc4\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\52f2c05360ea4e8df159f4ff2767567b\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c444e506da6c2e4e683107f72766f0a2\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\a20a72c21acf2a21d60cd6f37a281562\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\63a7e7768ded116a2418308c87bd948c\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ea2565f054a1b82abdbc0af0b0c9a3d4\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f621545c5cc72f129f50de6053b52a1a\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\98ebe932c34efba361490b6fec49f11d\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7a63affec60ef7885834660223fc2c6\transformed\media3-extractor-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\835ae3164c6ad9004df3ac1c07239825\transformed\media3-container-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cc6e09995cc7c2c69c94c01de4bd0da\transformed\media3-datasource-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cb1e24519e96e15a66831550542c874f\transformed\media3-decoder-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7851aaa3a5d1dc734d59e932c136b6d5\transformed\media3-database-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ba54782ed0a0237edf267eb65f979c4\transformed\media3-common-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\efcfa4e78b3b22010a075bf53629cfd5\transformed\media3-ui-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\1d30012eb235080eb1b237eb0b3565ba\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\0cfb5476e0ef47f5d477350764dc5908\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cb234f7eca71d4e68f1ba62ce1888794\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\96b2a01ee678d9fe2b553dc9823f3992\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\d4bdb7027aabad89157044ab01242c64\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\7dff010d2e850ce0dac913186ad83659\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\5c03e22f086bcd8576b77569ed7807e3\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e80f2aaf8725937ea4661dc3fdb86307\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\858212b3e00315cb455a8c0c93ea0798\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d4a42ddbffed190ed15b8f8af92fc6f1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\951eb23f800c5d30a89fe65dc95573cb\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c5d8d93e1818cb8ede575c471b96add\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b80837b1c6c3c9f9effe821bc6f8b43\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e296d5000760688ed70176b5ef94b11c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc066e1ab6a308f740e26c00bb2aab6a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fbefa4ef376f20b472ea2a395a6326b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\104fad634bea1bef1d94a5e002f8221a\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\50575c36502d4d401686be9c424d7d4a\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\70a17e98daf6294f40e81dbb63ab6f6e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\601319a6c10c94f6cac814cde611d658\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\db83a3119b276e44c421e7ca274cfd23\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbb956b0abbb9d56571215f5005819de\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\26b9fcd62c27617fff0e84d4c337ac1f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1912bfc0492038cbf1908b39754b068\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4d3e9818518119054154ec4ed0ce45b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f652478a5006d18283b5a5ecf36b2638\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0279dcc2c3aa53f03072333acd599b4e\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72f513d3ee3afc0aed5480c1bf27e612\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e174a56339c3ccfb29a9a2478a19a823\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7c9333afd39caca29cc966e6a38984\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac8bebec7e7a4c8abb21ef375ca1b90\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\046297a960e10c47d92cadd5829fe2cb\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4eb8dd7084db99cdcf299981f253197f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\be684dd8f1cfe0ff60e8162beb6b065d\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7894cef2e2c43e825347f2da299f96f\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c6c94bed606ea9776c3233081061c38\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a099e24f33bfb28acc401602b44ee1c\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\96662df6fc326f414d8256b1c1527d0d\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb8efd5624dd75fea6bedf93ce9aa608\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b8fd878ba124495e89519ab31f7080\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6908f8cfe6605e194d2d58412cc1cefe\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68bb2667586b566dc8e4f123b2df03bc\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdbaf1a7a5cb6049a4a942e2be7853fc\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32b8cefb7ddb932c1d8c035fa5f76ea8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45fbfc0aa88bb4b939be71f581d26793\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8a55f0f141c3be38da8822a23d8ac6e7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\b1696cdd930e109e5bfa2036eec671bb\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\348869ebefd18ad62c3d31c1e60b07cf\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aee7f0a6179e4819ee538594a4ff4835\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ef51e806a2fc62af65213767f26cbbad\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\60f436ed1e34d6cb1e441f972d2276eb\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\124887430f17e404f9e542d41a937a90\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a08588b203f5405d9935739daf7b04c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2d52a78bae319b82169a9a50e4fcded\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\3fec12e532ab71a2357814e9625645d8\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\98ebe932c34efba361490b6fec49f11d\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\98ebe932c34efba361490b6fec49f11d\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ba54782ed0a0237edf267eb65f979c4\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ba54782ed0a0237edf267eb65f979c4\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:12:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:15:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:15:22-65
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:18:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:18:22-77
application
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:20:5-73:19
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:20:5-73:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec96043c8b7c92d308a76e9418d0c48a\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec96043c8b7c92d308a76e9418d0c48a\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3aa0627e5148a63948b207e42496341e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3aa0627e5148a63948b207e42496341e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb321b3db659c06706ae4e76d9f2d26\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb321b3db659c06706ae4e76d9f2d26\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8a55f0f141c3be38da8822a23d8ac6e7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8a55f0f141c3be38da8822a23d8ac6e7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aee7f0a6179e4819ee538594a4ff4835\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aee7f0a6179e4819ee538594a4ff4835\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:28:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:26:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:24:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:27:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:31:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:25:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:22:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:29:9-55
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:23:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:30:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:21:9-40
activity#com.badboyz.iptvplayer.ui.main.MainActivity
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:34:9-43:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:37:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:36:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:38:13-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:35:13-49
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:39:13-42:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:41:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:41:27-74
activity#com.badboyz.iptvplayer.ui.player.PlayerActivity
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:46:9-51:73
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:50:13-50
	android:exported
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:48:13-37
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:49:13-74
	android:theme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:51:13-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:47:13-53
activity#com.badboyz.iptvplayer.ui.splash.SplashActivity
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:54:9-65:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:56:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:57:13-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:55:13-53
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:host:*+data:pathPattern:.*\\.m3u+data:pathPattern:.*\\.m3u+data:scheme:http+data:scheme:https
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:58:13-64:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:59:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:59:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:60:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:60:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:61:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:61:27-75
data
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:17-95
	android:host
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:45-61
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:23-44
	android:pathPattern
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:62-92
activity#com.badboyz.iptvplayer.ui.settings.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:68:9-71:66
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:71:13-63
	android:exported
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:70:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:69:13-57
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f005559fa46d9d9f267bc6dc21b6f74a\transformed\databinding-adapters-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f005559fa46d9d9f267bc6dc21b6f74a\transformed\databinding-adapters-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\a84b45771e0eb8c803e9f6b7e1ba65f7\transformed\databinding-ktx-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\a84b45771e0eb8c803e9f6b7e1ba65f7\transformed\databinding-ktx-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8c0f19a396f58f0891f175b9b10c21c1\transformed\databinding-runtime-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8c0f19a396f58f0891f175b9b10c21c1\transformed\databinding-runtime-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\7921d919f8b5092c5acfbfa4bf05e791\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\7921d919f8b5092c5acfbfa4bf05e791\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\7f60846c1ae9e86d0e4a59bf7c8de070\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\7f60846c1ae9e86d0e4a59bf7c8de070\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\49b6ee58b8c1000a05168da9ce4f8212\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\49b6ee58b8c1000a05168da9ce4f8212\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\289eef689cd649c2f0fe72dfdb28bce4\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\289eef689cd649c2f0fe72dfdb28bce4\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\64d5d5a1489960e9e0388ea585384201\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\64d5d5a1489960e9e0388ea585384201\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\3941269e0a272f94f270edb2b4a55440\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\3941269e0a272f94f270edb2b4a55440\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\352ab48c38c648a8a5d49c36bbab4b79\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\352ab48c38c648a8a5d49c36bbab4b79\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\42579f1f4a796df696b1d64b7ad2a0c2\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\42579f1f4a796df696b1d64b7ad2a0c2\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\4759b026881da3a3aff1b5bbcb3454af\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\4759b026881da3a3aff1b5bbcb3454af\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec96043c8b7c92d308a76e9418d0c48a\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec96043c8b7c92d308a76e9418d0c48a\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3aa0627e5148a63948b207e42496341e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3aa0627e5148a63948b207e42496341e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb321b3db659c06706ae4e76d9f2d26\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb321b3db659c06706ae4e76d9f2d26\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2b00f4d78e206fdb7d67bddbe7f714b7\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2b00f4d78e206fdb7d67bddbe7f714b7\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\dcafc80b61e93bbcb116fecec747e0b5\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\dcafc80b61e93bbcb116fecec747e0b5\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b89cf8e779a00d2af6b7386c87a67af\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b89cf8e779a00d2af6b7386c87a67af\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\b5b0f4f13ed49f48ce7a093f200f4ebd\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\b5b0f4f13ed49f48ce7a093f200f4ebd\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\185df9d2eb29b059f6dfbcd869d39a86\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\185df9d2eb29b059f6dfbcd869d39a86\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c86fa7cfe92505d5e17180c34c40adbc\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c86fa7cfe92505d5e17180c34c40adbc\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c963dd3299b7f63d478d26118d5d649b\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c963dd3299b7f63d478d26118d5d649b\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\aed7e083c287157aafa2ed63e56f741f\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\aed7e083c287157aafa2ed63e56f741f\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6f7bf611fd22f9cf1dc5d6d69e9e1d4\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6f7bf611fd22f9cf1dc5d6d69e9e1d4\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac122f2b9edcb317f3f8ea7fd600df67\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac122f2b9edcb317f3f8ea7fd600df67\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8bf1e03e740445a3b0bce7c5b8eef56c\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8bf1e03e740445a3b0bce7c5b8eef56c\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\046015eaa4de80232fad8306c7a50ce6\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\046015eaa4de80232fad8306c7a50ce6\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8e3fdc2940e77d62dd61bcb70032b5a9\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8e3fdc2940e77d62dd61bcb70032b5a9\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7c74f49a41b480ba85efd06127adef\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7c74f49a41b480ba85efd06127adef\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c24d876e0a38ed7b6c94aba6260f347\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c24d876e0a38ed7b6c94aba6260f347\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4a0cc5e8bed9707a9c3275f89240bc4\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4a0cc5e8bed9707a9c3275f89240bc4\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\52f2c05360ea4e8df159f4ff2767567b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\52f2c05360ea4e8df159f4ff2767567b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c444e506da6c2e4e683107f72766f0a2\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c444e506da6c2e4e683107f72766f0a2\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\a20a72c21acf2a21d60cd6f37a281562\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\a20a72c21acf2a21d60cd6f37a281562\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\63a7e7768ded116a2418308c87bd948c\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\63a7e7768ded116a2418308c87bd948c\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ea2565f054a1b82abdbc0af0b0c9a3d4\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ea2565f054a1b82abdbc0af0b0c9a3d4\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f621545c5cc72f129f50de6053b52a1a\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f621545c5cc72f129f50de6053b52a1a\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\98ebe932c34efba361490b6fec49f11d\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\98ebe932c34efba361490b6fec49f11d\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7a63affec60ef7885834660223fc2c6\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7a63affec60ef7885834660223fc2c6\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\835ae3164c6ad9004df3ac1c07239825\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\835ae3164c6ad9004df3ac1c07239825\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cc6e09995cc7c2c69c94c01de4bd0da\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cc6e09995cc7c2c69c94c01de4bd0da\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cb1e24519e96e15a66831550542c874f\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cb1e24519e96e15a66831550542c874f\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7851aaa3a5d1dc734d59e932c136b6d5\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7851aaa3a5d1dc734d59e932c136b6d5\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ba54782ed0a0237edf267eb65f979c4\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ba54782ed0a0237edf267eb65f979c4\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\efcfa4e78b3b22010a075bf53629cfd5\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\efcfa4e78b3b22010a075bf53629cfd5\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\1d30012eb235080eb1b237eb0b3565ba\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\1d30012eb235080eb1b237eb0b3565ba\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\0cfb5476e0ef47f5d477350764dc5908\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\0cfb5476e0ef47f5d477350764dc5908\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cb234f7eca71d4e68f1ba62ce1888794\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cb234f7eca71d4e68f1ba62ce1888794\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\96b2a01ee678d9fe2b553dc9823f3992\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\96b2a01ee678d9fe2b553dc9823f3992\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\d4bdb7027aabad89157044ab01242c64\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\d4bdb7027aabad89157044ab01242c64\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\7dff010d2e850ce0dac913186ad83659\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\7dff010d2e850ce0dac913186ad83659\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\5c03e22f086bcd8576b77569ed7807e3\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\5c03e22f086bcd8576b77569ed7807e3\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e80f2aaf8725937ea4661dc3fdb86307\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e80f2aaf8725937ea4661dc3fdb86307\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\858212b3e00315cb455a8c0c93ea0798\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\858212b3e00315cb455a8c0c93ea0798\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d4a42ddbffed190ed15b8f8af92fc6f1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d4a42ddbffed190ed15b8f8af92fc6f1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\951eb23f800c5d30a89fe65dc95573cb\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\951eb23f800c5d30a89fe65dc95573cb\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c5d8d93e1818cb8ede575c471b96add\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c5d8d93e1818cb8ede575c471b96add\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b80837b1c6c3c9f9effe821bc6f8b43\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b80837b1c6c3c9f9effe821bc6f8b43\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e296d5000760688ed70176b5ef94b11c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e296d5000760688ed70176b5ef94b11c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc066e1ab6a308f740e26c00bb2aab6a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc066e1ab6a308f740e26c00bb2aab6a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fbefa4ef376f20b472ea2a395a6326b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fbefa4ef376f20b472ea2a395a6326b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\104fad634bea1bef1d94a5e002f8221a\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\104fad634bea1bef1d94a5e002f8221a\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\50575c36502d4d401686be9c424d7d4a\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\50575c36502d4d401686be9c424d7d4a\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\70a17e98daf6294f40e81dbb63ab6f6e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\70a17e98daf6294f40e81dbb63ab6f6e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\601319a6c10c94f6cac814cde611d658\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\601319a6c10c94f6cac814cde611d658\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\db83a3119b276e44c421e7ca274cfd23\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\db83a3119b276e44c421e7ca274cfd23\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbb956b0abbb9d56571215f5005819de\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbb956b0abbb9d56571215f5005819de\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\26b9fcd62c27617fff0e84d4c337ac1f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\26b9fcd62c27617fff0e84d4c337ac1f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1912bfc0492038cbf1908b39754b068\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1912bfc0492038cbf1908b39754b068\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4d3e9818518119054154ec4ed0ce45b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4d3e9818518119054154ec4ed0ce45b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f652478a5006d18283b5a5ecf36b2638\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f652478a5006d18283b5a5ecf36b2638\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0279dcc2c3aa53f03072333acd599b4e\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0279dcc2c3aa53f03072333acd599b4e\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72f513d3ee3afc0aed5480c1bf27e612\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72f513d3ee3afc0aed5480c1bf27e612\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e174a56339c3ccfb29a9a2478a19a823\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e174a56339c3ccfb29a9a2478a19a823\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7c9333afd39caca29cc966e6a38984\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7c9333afd39caca29cc966e6a38984\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac8bebec7e7a4c8abb21ef375ca1b90\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac8bebec7e7a4c8abb21ef375ca1b90\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\046297a960e10c47d92cadd5829fe2cb\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\046297a960e10c47d92cadd5829fe2cb\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4eb8dd7084db99cdcf299981f253197f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4eb8dd7084db99cdcf299981f253197f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\be684dd8f1cfe0ff60e8162beb6b065d\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\be684dd8f1cfe0ff60e8162beb6b065d\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7894cef2e2c43e825347f2da299f96f\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7894cef2e2c43e825347f2da299f96f\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c6c94bed606ea9776c3233081061c38\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c6c94bed606ea9776c3233081061c38\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a099e24f33bfb28acc401602b44ee1c\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a099e24f33bfb28acc401602b44ee1c\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\96662df6fc326f414d8256b1c1527d0d\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\96662df6fc326f414d8256b1c1527d0d\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb8efd5624dd75fea6bedf93ce9aa608\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb8efd5624dd75fea6bedf93ce9aa608\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b8fd878ba124495e89519ab31f7080\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b8fd878ba124495e89519ab31f7080\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6908f8cfe6605e194d2d58412cc1cefe\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6908f8cfe6605e194d2d58412cc1cefe\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68bb2667586b566dc8e4f123b2df03bc\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68bb2667586b566dc8e4f123b2df03bc\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdbaf1a7a5cb6049a4a942e2be7853fc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdbaf1a7a5cb6049a4a942e2be7853fc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32b8cefb7ddb932c1d8c035fa5f76ea8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32b8cefb7ddb932c1d8c035fa5f76ea8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45fbfc0aa88bb4b939be71f581d26793\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45fbfc0aa88bb4b939be71f581d26793\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8a55f0f141c3be38da8822a23d8ac6e7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8a55f0f141c3be38da8822a23d8ac6e7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\b1696cdd930e109e5bfa2036eec671bb\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\b1696cdd930e109e5bfa2036eec671bb\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\348869ebefd18ad62c3d31c1e60b07cf\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\348869ebefd18ad62c3d31c1e60b07cf\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aee7f0a6179e4819ee538594a4ff4835\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aee7f0a6179e4819ee538594a4ff4835\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ef51e806a2fc62af65213767f26cbbad\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ef51e806a2fc62af65213767f26cbbad\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\60f436ed1e34d6cb1e441f972d2276eb\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\60f436ed1e34d6cb1e441f972d2276eb\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\124887430f17e404f9e542d41a937a90\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\124887430f17e404f9e542d41a937a90\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a08588b203f5405d9935739daf7b04c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a08588b203f5405d9935739daf7b04c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2d52a78bae319b82169a9a50e4fcded\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2d52a78bae319b82169a9a50e4fcded\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\3fec12e532ab71a2357814e9625645d8\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\3fec12e532ab71a2357814e9625645d8\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aee7f0a6179e4819ee538594a4ff4835\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aee7f0a6179e4819ee538594a4ff4835\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.badboyz.iptvplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.badboyz.iptvplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
