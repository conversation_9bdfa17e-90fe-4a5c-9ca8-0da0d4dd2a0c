-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:1-75:12
MERGED from [androidx.databinding:databinding-adapters:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\c37f6782d684fda28c5281a549247e5f\transformed\databinding-adapters-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ef7746492e9020d9a3a0d20c98569bfc\transformed\databinding-ktx-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\14c8de326c56f327ae4e6fe61713d18c\transformed\databinding-runtime-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b35b32ac8d593c8da35fdbab31099d1\transformed\viewbinding-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\79ce526096da35edcc1dff8083e15ac2\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\90fa9ed7abf7a04ef13fe5c16ca7237f\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\9ad8fe4af225661978a326dcf65e2faa\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\6b356a896c53d2ea2fa729b1c6296e63\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\562596de0df8eff1d12664ffb75dd7eb\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\11bf1c23ac9086349f87728d6251a46a\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\4450d914b6f7fb7da7e1a004053f2486\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\3c06492a962edc10cb4afd995bdc9961\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\1c30a6e1576391bb1585dd679e4d5216\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c711e708460550b0e6d2719a43fe6f2e\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\ec82856b4280e5ce454d5cb8b90ad303\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2befaf38d62cf74c0cfdc14f376f5481\transformed\lottie-6.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\eb650a6adbe942a8f27d67e9b239e3d6\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f6383dc1c59a74ffea8dbe469a360a8\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\bf78663ddf7e5a17052df8a5e558236f\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\2ac655e86d0f741db217733fee6db437\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\e7474dbe81423d135a1f5eca171af61e\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\9ed136abd262e19cc53c96158716b91e\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\775511a18ee1db97ddf681908db7356f\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\4d4105d7cff91258421fe72475c4be23\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\993a6cf23883cfce3779ee597e60f02e\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\520009843512d9cbe8f9446c49a5c83a\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c8d9adaacf81e611108b0fc46989a357\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\fcb81c0ff61658b529102ffa21be4bbe\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\46a8bbfd221f7dbb6f93614eee55ff8d\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c0a328f966d2bd16ae96e29763e983a7\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\0f4d9cd7d6c3561bfdb86733291679f9\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\eeda26ae2856c32f023f62d19c60ccf5\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\3bca783f1693785c71975621b3954b5e\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\dc43630496df2a2c56ccb0e99e7bdb91\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\75dfc887c2e55f7109abeb89dd83d79b\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\efe0e3583596feeea587a194dc746a51\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\f9e2920226625b05aa1366e1b40c7b02\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9104232f4feceecda5897b1939980a29\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f897737c94ec656216f34ce742db0f91\transformed\exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\8c441e88d76142b996bad90a67757093\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b536b70178c125cda2af87e16924ecc\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\2398419fbc499cc71bc575bc9cd9abdd\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\19d6e5ee187264d537197e51eeb3cb8c\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\52e8572eea82d082397cf653b2e0de72\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c995cec1db1f45bfe2d74450da604333\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c2302182cfe946eb6228dba1e330391f\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\d89ea39d8cf8796cb13c3d54a65685fb\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\71de291c7233fd078c0d2f0e10743497\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5de0fe563a91b15fd75f7e7db71436ce\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\fdae804873551abaaa22ad4dd6082578\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c3dc87e68fb2028a82ccda8351610a85\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\efb7cf40d74783078b97468960aa8d83\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b838afa9a53091a4c694ee0c53efc90\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6dde0c04c40b35a71a1be7274387d5b8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c5aed0aa2c7616f6dd4d95214c119873\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c62644203ad0f8957e6bbf457dc54252\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5cb2da204b9ec4b8ccfd0fbdb9514451\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5632ea460fd63abe59f47dd8ed4d720\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\539bf094cf03b9869892dfef69acba9a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac5736eb317b3245897ffcff47b79797\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e55bb85abc395cc8165091b9c1b4e63\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf76e23a984aa087c1e2e75fc390f288\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f20a1f4067155cf1a8a555af9cbc9b59\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bacff12b9e9044910ba96e05de5e4f77\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0314da4490cef30bee7cbef8cc11de90\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\77e46235985ada5d1900da1e734c4f30\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\8852f637319962b0f43fe0bea9f60e35\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\c7c17d3dd0c3cdac0004436b35e87a8b\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9f35a576b6332bb7802effb36b959b9\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\1f4558d386295dd298977f5591f76a96\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c9ad5896dbe4b4ab7793f66459dd36f\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\09035b6f7ce5f7c700c5ef0da4ae329c\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf0cf8abcd05b3b386ac251dc64393af\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d499412b298319cfaaecb39842a82d7\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\760c8dbe92a2450b5c47e7a1936cb292\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61ad11c668c46c73b6918d74abe63f88\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\ec3983f1651bd542ec977772017d0051\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5220978c29bb495cbff5fc03ccd1edc4\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\45758e1efb5cca10fd95403ce2933847\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f9686259ea8a3a8c9ebeb4f520ea1fcc\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\9314da07a19a02391cace6bde0f64b80\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fffc84cb9e240125189cd83954499ee6\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\773888424aff26515efd080e80704fdf\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\30e1f0c01b4f854e11163a5904127a0d\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b577022f14186b9146af7616d93f06a5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac016f34e20d153eeba7c35a6ee33e84\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ccb40d6182dab56dcd49123fe29a8d8c\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf763e6cf371562f7d39a73f2877e8e8\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2920fbf1dadfaa5b908ece9ef0bc8089\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa8d38ce2f6dee841a8cc83178f8412e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f35225b19f10a9c99e231d4406d49e97\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\a87b550b5780e05b21ee2147687286cb\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\e638672c555bf63fc2d65d6c718adbd4\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\182d955a6fd700feaaf9a872529483c9\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f6e601aa5ee865d6c8b5a74d4fc9f0a3\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bf17f4fd7e380aa6c0d40943d5f27df\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\d438ca3a67e044f28f2b9e8a5dc3a074\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\647968f965029aea1cfcc7b7fb859ea4\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc34092b5c3ba7ae557269029269323\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a746f0c3727d4e10f25e779b85b3ca6\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72b0fcd5d5db8a2c8e4c49796f516643\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a8342f54eee4c9b6edee33a43e41c08d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e31a933f0a779f2f666e11dac4ffa41\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65f4121f7fe94162063a3dee617b42fd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d7ea21103e9c5971a8a8d3a2f2cf1129\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\bf1e3f43a64e46124122ecd4df801232\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c9ad5896dbe4b4ab7793f66459dd36f\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c9ad5896dbe4b4ab7793f66459dd36f\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bf17f4fd7e380aa6c0d40943d5f27df\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bf17f4fd7e380aa6c0d40943d5f27df\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:12:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:15:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:15:22-65
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:18:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:18:22-77
application
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:20:5-73:19
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:20:5-73:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\79ce526096da35edcc1dff8083e15ac2\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\79ce526096da35edcc1dff8083e15ac2\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c711e708460550b0e6d2719a43fe6f2e\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c711e708460550b0e6d2719a43fe6f2e\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\ec82856b4280e5ce454d5cb8b90ad303\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\ec82856b4280e5ce454d5cb8b90ad303\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2befaf38d62cf74c0cfdc14f376f5481\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2befaf38d62cf74c0cfdc14f376f5481\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\dc43630496df2a2c56ccb0e99e7bdb91\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\dc43630496df2a2c56ccb0e99e7bdb91\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\f9e2920226625b05aa1366e1b40c7b02\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\f9e2920226625b05aa1366e1b40c7b02\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc34092b5c3ba7ae557269029269323\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc34092b5c3ba7ae557269029269323\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a8342f54eee4c9b6edee33a43e41c08d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a8342f54eee4c9b6edee33a43e41c08d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:28:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:26:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:24:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:27:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:31:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:25:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:22:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:29:9-55
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:23:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:30:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:21:9-40
activity#com.badboyz.iptvplayer.ui.main.MainActivity
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:34:9-43:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:37:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:36:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:38:13-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:35:13-49
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:39:13-42:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:41:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:41:27-74
activity#com.badboyz.iptvplayer.ui.player.PlayerActivity
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:46:9-51:73
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:50:13-50
	android:exported
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:48:13-37
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:49:13-74
	android:theme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:51:13-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:47:13-53
activity#com.badboyz.iptvplayer.ui.splash.SplashActivity
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:54:9-65:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:56:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:57:13-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:55:13-53
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:host:*+data:pathPattern:.*\\.m3u+data:pathPattern:.*\\.m3u+data:scheme:http+data:scheme:https
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:58:13-64:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:59:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:59:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:60:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:60:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:61:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:61:27-75
data
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:17-95
	android:host
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:45-61
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:23-44
	android:pathPattern
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:62-92
activity#com.badboyz.iptvplayer.ui.settings.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:68:9-71:66
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:71:13-63
	android:exported
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:70:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:69:13-57
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\c37f6782d684fda28c5281a549247e5f\transformed\databinding-adapters-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\c37f6782d684fda28c5281a549247e5f\transformed\databinding-adapters-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ef7746492e9020d9a3a0d20c98569bfc\transformed\databinding-ktx-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ef7746492e9020d9a3a0d20c98569bfc\transformed\databinding-ktx-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\14c8de326c56f327ae4e6fe61713d18c\transformed\databinding-runtime-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\14c8de326c56f327ae4e6fe61713d18c\transformed\databinding-runtime-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b35b32ac8d593c8da35fdbab31099d1\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b35b32ac8d593c8da35fdbab31099d1\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\79ce526096da35edcc1dff8083e15ac2\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\79ce526096da35edcc1dff8083e15ac2\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\90fa9ed7abf7a04ef13fe5c16ca7237f\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\90fa9ed7abf7a04ef13fe5c16ca7237f\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\9ad8fe4af225661978a326dcf65e2faa\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\9ad8fe4af225661978a326dcf65e2faa\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\6b356a896c53d2ea2fa729b1c6296e63\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\6b356a896c53d2ea2fa729b1c6296e63\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\562596de0df8eff1d12664ffb75dd7eb\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\562596de0df8eff1d12664ffb75dd7eb\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\11bf1c23ac9086349f87728d6251a46a\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\11bf1c23ac9086349f87728d6251a46a\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\4450d914b6f7fb7da7e1a004053f2486\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\4450d914b6f7fb7da7e1a004053f2486\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\3c06492a962edc10cb4afd995bdc9961\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\3c06492a962edc10cb4afd995bdc9961\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\1c30a6e1576391bb1585dd679e4d5216\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-4\1c30a6e1576391bb1585dd679e4d5216\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c711e708460550b0e6d2719a43fe6f2e\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c711e708460550b0e6d2719a43fe6f2e\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\ec82856b4280e5ce454d5cb8b90ad303\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\ec82856b4280e5ce454d5cb8b90ad303\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2befaf38d62cf74c0cfdc14f376f5481\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2befaf38d62cf74c0cfdc14f376f5481\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\eb650a6adbe942a8f27d67e9b239e3d6\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\eb650a6adbe942a8f27d67e9b239e3d6\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f6383dc1c59a74ffea8dbe469a360a8\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f6383dc1c59a74ffea8dbe469a360a8\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\bf78663ddf7e5a17052df8a5e558236f\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\bf78663ddf7e5a17052df8a5e558236f\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\2ac655e86d0f741db217733fee6db437\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\2ac655e86d0f741db217733fee6db437\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\e7474dbe81423d135a1f5eca171af61e\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\e7474dbe81423d135a1f5eca171af61e\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\9ed136abd262e19cc53c96158716b91e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\9ed136abd262e19cc53c96158716b91e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\775511a18ee1db97ddf681908db7356f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\775511a18ee1db97ddf681908db7356f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\4d4105d7cff91258421fe72475c4be23\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\4d4105d7cff91258421fe72475c4be23\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\993a6cf23883cfce3779ee597e60f02e\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\993a6cf23883cfce3779ee597e60f02e\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\520009843512d9cbe8f9446c49a5c83a\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\520009843512d9cbe8f9446c49a5c83a\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c8d9adaacf81e611108b0fc46989a357\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c8d9adaacf81e611108b0fc46989a357\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\fcb81c0ff61658b529102ffa21be4bbe\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\fcb81c0ff61658b529102ffa21be4bbe\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\46a8bbfd221f7dbb6f93614eee55ff8d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\46a8bbfd221f7dbb6f93614eee55ff8d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c0a328f966d2bd16ae96e29763e983a7\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c0a328f966d2bd16ae96e29763e983a7\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\0f4d9cd7d6c3561bfdb86733291679f9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\0f4d9cd7d6c3561bfdb86733291679f9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\eeda26ae2856c32f023f62d19c60ccf5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\eeda26ae2856c32f023f62d19c60ccf5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\3bca783f1693785c71975621b3954b5e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\3bca783f1693785c71975621b3954b5e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\dc43630496df2a2c56ccb0e99e7bdb91\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\dc43630496df2a2c56ccb0e99e7bdb91\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\75dfc887c2e55f7109abeb89dd83d79b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\75dfc887c2e55f7109abeb89dd83d79b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\efe0e3583596feeea587a194dc746a51\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\efe0e3583596feeea587a194dc746a51\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\f9e2920226625b05aa1366e1b40c7b02\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\f9e2920226625b05aa1366e1b40c7b02\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9104232f4feceecda5897b1939980a29\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9104232f4feceecda5897b1939980a29\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f897737c94ec656216f34ce742db0f91\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f897737c94ec656216f34ce742db0f91\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\8c441e88d76142b996bad90a67757093\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\8c441e88d76142b996bad90a67757093\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b536b70178c125cda2af87e16924ecc\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b536b70178c125cda2af87e16924ecc\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\2398419fbc499cc71bc575bc9cd9abdd\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\2398419fbc499cc71bc575bc9cd9abdd\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\19d6e5ee187264d537197e51eeb3cb8c\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\19d6e5ee187264d537197e51eeb3cb8c\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\52e8572eea82d082397cf653b2e0de72\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\52e8572eea82d082397cf653b2e0de72\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c995cec1db1f45bfe2d74450da604333\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c995cec1db1f45bfe2d74450da604333\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c2302182cfe946eb6228dba1e330391f\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c2302182cfe946eb6228dba1e330391f\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\d89ea39d8cf8796cb13c3d54a65685fb\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\d89ea39d8cf8796cb13c3d54a65685fb\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\71de291c7233fd078c0d2f0e10743497\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\71de291c7233fd078c0d2f0e10743497\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5de0fe563a91b15fd75f7e7db71436ce\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5de0fe563a91b15fd75f7e7db71436ce\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\fdae804873551abaaa22ad4dd6082578\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\fdae804873551abaaa22ad4dd6082578\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c3dc87e68fb2028a82ccda8351610a85\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c3dc87e68fb2028a82ccda8351610a85\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\efb7cf40d74783078b97468960aa8d83\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\efb7cf40d74783078b97468960aa8d83\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b838afa9a53091a4c694ee0c53efc90\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b838afa9a53091a4c694ee0c53efc90\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6dde0c04c40b35a71a1be7274387d5b8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6dde0c04c40b35a71a1be7274387d5b8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c5aed0aa2c7616f6dd4d95214c119873\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c5aed0aa2c7616f6dd4d95214c119873\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c62644203ad0f8957e6bbf457dc54252\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c62644203ad0f8957e6bbf457dc54252\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5cb2da204b9ec4b8ccfd0fbdb9514451\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5cb2da204b9ec4b8ccfd0fbdb9514451\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5632ea460fd63abe59f47dd8ed4d720\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5632ea460fd63abe59f47dd8ed4d720\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\539bf094cf03b9869892dfef69acba9a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\539bf094cf03b9869892dfef69acba9a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac5736eb317b3245897ffcff47b79797\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac5736eb317b3245897ffcff47b79797\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e55bb85abc395cc8165091b9c1b4e63\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e55bb85abc395cc8165091b9c1b4e63\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf76e23a984aa087c1e2e75fc390f288\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf76e23a984aa087c1e2e75fc390f288\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f20a1f4067155cf1a8a555af9cbc9b59\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f20a1f4067155cf1a8a555af9cbc9b59\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bacff12b9e9044910ba96e05de5e4f77\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bacff12b9e9044910ba96e05de5e4f77\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0314da4490cef30bee7cbef8cc11de90\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0314da4490cef30bee7cbef8cc11de90\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\77e46235985ada5d1900da1e734c4f30\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\77e46235985ada5d1900da1e734c4f30\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\8852f637319962b0f43fe0bea9f60e35\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\8852f637319962b0f43fe0bea9f60e35\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\c7c17d3dd0c3cdac0004436b35e87a8b\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\c7c17d3dd0c3cdac0004436b35e87a8b\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9f35a576b6332bb7802effb36b959b9\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9f35a576b6332bb7802effb36b959b9\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\1f4558d386295dd298977f5591f76a96\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\1f4558d386295dd298977f5591f76a96\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c9ad5896dbe4b4ab7793f66459dd36f\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c9ad5896dbe4b4ab7793f66459dd36f\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\09035b6f7ce5f7c700c5ef0da4ae329c\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\09035b6f7ce5f7c700c5ef0da4ae329c\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf0cf8abcd05b3b386ac251dc64393af\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf0cf8abcd05b3b386ac251dc64393af\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d499412b298319cfaaecb39842a82d7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d499412b298319cfaaecb39842a82d7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\760c8dbe92a2450b5c47e7a1936cb292\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\760c8dbe92a2450b5c47e7a1936cb292\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61ad11c668c46c73b6918d74abe63f88\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61ad11c668c46c73b6918d74abe63f88\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\ec3983f1651bd542ec977772017d0051\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\ec3983f1651bd542ec977772017d0051\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5220978c29bb495cbff5fc03ccd1edc4\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5220978c29bb495cbff5fc03ccd1edc4\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\45758e1efb5cca10fd95403ce2933847\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\45758e1efb5cca10fd95403ce2933847\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f9686259ea8a3a8c9ebeb4f520ea1fcc\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f9686259ea8a3a8c9ebeb4f520ea1fcc\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\9314da07a19a02391cace6bde0f64b80\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\9314da07a19a02391cace6bde0f64b80\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fffc84cb9e240125189cd83954499ee6\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fffc84cb9e240125189cd83954499ee6\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\773888424aff26515efd080e80704fdf\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\773888424aff26515efd080e80704fdf\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\30e1f0c01b4f854e11163a5904127a0d\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\30e1f0c01b4f854e11163a5904127a0d\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b577022f14186b9146af7616d93f06a5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b577022f14186b9146af7616d93f06a5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac016f34e20d153eeba7c35a6ee33e84\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac016f34e20d153eeba7c35a6ee33e84\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ccb40d6182dab56dcd49123fe29a8d8c\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ccb40d6182dab56dcd49123fe29a8d8c\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf763e6cf371562f7d39a73f2877e8e8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf763e6cf371562f7d39a73f2877e8e8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2920fbf1dadfaa5b908ece9ef0bc8089\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2920fbf1dadfaa5b908ece9ef0bc8089\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa8d38ce2f6dee841a8cc83178f8412e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa8d38ce2f6dee841a8cc83178f8412e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f35225b19f10a9c99e231d4406d49e97\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f35225b19f10a9c99e231d4406d49e97\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\a87b550b5780e05b21ee2147687286cb\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\a87b550b5780e05b21ee2147687286cb\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\e638672c555bf63fc2d65d6c718adbd4\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\e638672c555bf63fc2d65d6c718adbd4\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\182d955a6fd700feaaf9a872529483c9\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\182d955a6fd700feaaf9a872529483c9\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f6e601aa5ee865d6c8b5a74d4fc9f0a3\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\f6e601aa5ee865d6c8b5a74d4fc9f0a3\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bf17f4fd7e380aa6c0d40943d5f27df\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bf17f4fd7e380aa6c0d40943d5f27df\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\d438ca3a67e044f28f2b9e8a5dc3a074\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\d438ca3a67e044f28f2b9e8a5dc3a074\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\647968f965029aea1cfcc7b7fb859ea4\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\647968f965029aea1cfcc7b7fb859ea4\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc34092b5c3ba7ae557269029269323\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc34092b5c3ba7ae557269029269323\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a746f0c3727d4e10f25e779b85b3ca6\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a746f0c3727d4e10f25e779b85b3ca6\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72b0fcd5d5db8a2c8e4c49796f516643\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72b0fcd5d5db8a2c8e4c49796f516643\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a8342f54eee4c9b6edee33a43e41c08d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a8342f54eee4c9b6edee33a43e41c08d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e31a933f0a779f2f666e11dac4ffa41\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e31a933f0a779f2f666e11dac4ffa41\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65f4121f7fe94162063a3dee617b42fd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65f4121f7fe94162063a3dee617b42fd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d7ea21103e9c5971a8a8d3a2f2cf1129\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d7ea21103e9c5971a8a8d3a2f2cf1129\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\bf1e3f43a64e46124122ecd4df801232\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\bf1e3f43a64e46124122ecd4df801232\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\79ce526096da35edcc1dff8083e15ac2\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\79ce526096da35edcc1dff8083e15ac2\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\79ce526096da35edcc1dff8083e15ac2\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\dc43630496df2a2c56ccb0e99e7bdb91\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\dc43630496df2a2c56ccb0e99e7bdb91\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\dc43630496df2a2c56ccb0e99e7bdb91\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\f9e2920226625b05aa1366e1b40c7b02\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\f9e2920226625b05aa1366e1b40c7b02\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\f9e2920226625b05aa1366e1b40c7b02\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc34092b5c3ba7ae557269029269323\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc34092b5c3ba7ae557269029269323\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\aa0963311a84534a36861549732c9201\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\90908aa818e6158eea513035bc0bbe1d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcc9a4f87756532b72571b19f3d34857\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.badboyz.iptvplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.badboyz.iptvplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\7eb203db16c40ca18c3e77706606711f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a925603f230d8ffa790cafc11140765d\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1c2d47890e5f19ab240e9b10f638d9e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
