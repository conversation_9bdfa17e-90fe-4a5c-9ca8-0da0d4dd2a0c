package com.badboyz.iptvplayer.data.database.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.badboyz.iptvplayer.data.model.EPGProgram
import kotlinx.coroutines.flow.Flow

@Dao
interface EPGDao {
    
    @Query("SELECT * FROM epg_programs WHERE channelId = :channelId ORDER BY startTime ASC")
    fun getProgramsByChannel(channelId: String): Flow<List<EPGProgram>>
    
    @Query("SELECT * FROM epg_programs WHERE channelId = :channelId ORDER BY startTime ASC")
    fun getProgramsByChannelLiveData(channelId: String): LiveData<List<EPGProgram>>
    
    @Query("SELECT * FROM epg_programs WHERE channelId = :channelId AND startTime <= :currentTime AND endTime >= :currentTime LIMIT 1")
    suspend fun getCurrentProgram(channelId: String, currentTime: Long = System.currentTimeMillis()): EPGProgram?
    
    @Query("SELECT * FROM epg_programs WHERE channelId = :channelId AND startTime > :currentTime ORDER BY startTime ASC LIMIT 1")
    suspend fun getNextProgram(channelId: String, currentTime: Long = System.currentTimeMillis()): EPGProgram?
    
    @Query("SELECT * FROM epg_programs WHERE channelId = :channelId AND startTime >= :startTime AND startTime <= :endTime ORDER BY startTime ASC")
    fun getProgramsByChannelAndTimeRange(channelId: String, startTime: Long, endTime: Long): Flow<List<EPGProgram>>
    
    @Query("SELECT * FROM epg_programs WHERE title LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%'")
    fun searchPrograms(query: String): Flow<List<EPGProgram>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProgram(program: EPGProgram): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPrograms(programs: List<EPGProgram>)
    
    @Update
    suspend fun updateProgram(program: EPGProgram)
    
    @Delete
    suspend fun deleteProgram(program: EPGProgram)
    
    @Query("DELETE FROM epg_programs WHERE channelId = :channelId")
    suspend fun deleteProgramsByChannel(channelId: String)
    
    @Query("DELETE FROM epg_programs WHERE endTime < :timestamp")
    suspend fun deleteOldPrograms(timestamp: Long = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)) // 7 days ago
    
    @Query("DELETE FROM epg_programs")
    suspend fun deleteAllPrograms()
    
    @Query("SELECT COUNT(*) FROM epg_programs WHERE channelId = :channelId")
    suspend fun getProgramCountByChannel(channelId: String): Int
}
