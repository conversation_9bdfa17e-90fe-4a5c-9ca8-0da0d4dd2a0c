<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FF5722</color>
    <color name="background_primary">#121212</color>
    <color name="background_secondary">#1E1E1E</color>
    <color name="background_tertiary">#2D2D2D</color>
    <color name="black">#000000</color>
    <color name="card_background">#1E1E1E</color>
    <color name="card_background_selected">#2D2D2D</color>
    <color name="card_stroke">#333333</color>
    <color name="error">#F44336</color>
    <color name="gradient_end">#1565C0</color>
    <color name="gradient_start">#1E88E5</color>
    <color name="info">#2196F3</color>
    <color name="overlay_dark">#80000000</color>
    <color name="overlay_light">#4DFFFFFF</color>
    <color name="player_background">#000000</color>
    <color name="player_controls_background">#80000000</color>
    <color name="player_progress">#1E88E5</color>
    <color name="player_progress_background">#33FFFFFF</color>
    <color name="primary">#1E88E5</color>
    <color name="primary_dark">#1565C0</color>
    <color name="primary_light">#42A5F5</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="success">#4CAF50</color>
    <color name="surface">#1E1E1E</color>
    <color name="surface_variant">#2D2D2D</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_hint">#4DFFFFFF</color>
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#B3FFFFFF</color>
    <color name="text_tertiary">#80FFFFFF</color>
    <color name="transparent">#00000000</color>
    <color name="warning">#FF9800</color>
    <color name="white">#FFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    <dimen name="button_height">48dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_margin">8dp</dimen>
    <dimen name="card_padding">16dp</dimen>
    <dimen name="channel_item_height">80dp</dimen>
    <dimen name="channel_item_padding">16dp</dimen>
    <dimen name="channel_logo_size">56dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="input_field_height">56dp</dimen>
    <dimen name="list_item_height">72dp</dimen>
    <dimen name="list_item_padding_horizontal">16dp</dimen>
    <dimen name="list_item_padding_vertical">12dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="player_control_margin">16dp</dimen>
    <dimen name="player_control_size">48dp</dimen>
    <dimen name="spacing_large">24dp</dimen>
    <dimen name="spacing_medium">16dp</dimen>
    <dimen name="spacing_small">8dp</dimen>
    <dimen name="spacing_tiny">4dp</dimen>
    <dimen name="spacing_xlarge">32dp</dimen>
    <dimen name="text_size_caption">12sp</dimen>
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <string name="about">About</string>
    <string name="add_playlist">Add Playlist</string>
    <string name="add_playlist_title">Add New Playlist</string>
    <string name="add_to_favorites">Add to Favorites</string>
    <string name="all_channels">All Channels</string>
    <string name="app_name">BadBoyz IPTV Player</string>
    <string name="appearance_settings">Appearance</string>
    <string name="auto_theme">Auto Theme</string>
    <string name="buffer_size">Buffer Size</string>
    <string name="buffering">Buffering...</string>
    <string name="cancel">Cancel</string>
    <string name="cd_app_logo">App logo</string>
    <string name="cd_back_button">Back button</string>
    <string name="cd_channel_logo">Channel logo</string>
    <string name="cd_favorite_button">Favorite button</string>
    <string name="cd_menu_button">Menu button</string>
    <string name="cd_play_button">Play button</string>
    <string name="cd_playlist_icon">Playlist icon</string>
    <string name="cd_search_button">Search button</string>
    <string name="cd_settings_button">Settings button</string>
    <string name="channel_groups">Groups</string>
    <string name="channel_info">Channel Info</string>
    <string name="channels">Channels</string>
    <string name="clear_search">Clear search</string>
    <string name="confirm_delete_channel">Are you sure you want to delete this channel?</string>
    <string name="confirm_delete_playlist">Are you sure you want to delete this playlist?</string>
    <string name="connecting">Connecting...</string>
    <string name="connection_error">Connection error</string>
    <string name="connection_timeout">Connection Timeout</string>
    <string name="dark_theme">Dark Theme</string>
    <string name="date_format">MMM dd, yyyy</string>
    <string name="delete">Delete</string>
    <string name="delete_playlist">Delete Playlist</string>
    <string name="duration_format">%d:%02d</string>
    <string name="edit">Edit</string>
    <string name="edit_playlist_title">Edit Playlist</string>
    <string name="error_empty_name">Please enter a playlist name</string>
    <string name="error_empty_url">Please enter a playlist URL</string>
    <string name="error_invalid_url">Invalid URL</string>
    <string name="error_loading_playlist">Error loading playlist</string>
    <string name="error_network">Network error</string>
    <string name="error_playing_channel">Error playing channel</string>
    <string name="error_unknown">Unknown error occurred</string>
    <string name="exit_fullscreen">Exit Fullscreen</string>
    <string name="favorites">Favorites</string>
    <string name="fullscreen">Fullscreen</string>
    <string name="general_settings">General</string>
    <string name="home">Home</string>
    <string name="light_theme">Light Theme</string>
    <string name="live">LIVE</string>
    <string name="loading">Loading...</string>
    <string name="loading_channels">Loading channels...</string>
    <string name="loading_stream">Loading stream...</string>
    <string name="mute">Mute</string>
    <string name="my_playlists">My Playlists</string>
    <string name="next_program">Next Program</string>
    <string name="no">No</string>
    <string name="no_channels_found">No channels found</string>
    <string name="no_internet">No internet connection</string>
    <string name="no_search_results">No results found</string>
    <string name="now_playing">Now Playing</string>
    <string name="ok">OK</string>
    <string name="other">Other</string>
    <string name="pause">Pause</string>
    <string name="play">Play</string>
    <string name="playback_error">Playback error</string>
    <string name="player_settings">Player</string>
    <string name="playlist_added_successfully">Playlist added successfully</string>
    <string name="playlist_deleted_successfully">Playlist deleted successfully</string>
    <string name="playlist_name">Playlist Name</string>
    <string name="playlist_refreshed_successfully">Playlist refreshed successfully</string>
    <string name="playlist_updated_successfully">Playlist updated successfully</string>
    <string name="playlist_url">Playlist URL</string>
    <string name="playlist_url_hint">Enter M3U playlist URL (http:// or https://)</string>
    <string name="recently_watched">Recently Watched</string>
    <string name="refresh">Refresh</string>
    <string name="refresh_playlist">Refresh Playlist</string>
    <string name="remove_from_favorites">Remove from Favorites</string>
    <string name="save">Save</string>
    <string name="search">Search</string>
    <string name="search_channels">Search channels...</string>
    <string name="search_results">Search Results</string>
    <string name="settings">Settings</string>
    <string name="stop">Stop</string>
    <string name="stream_error">Stream error</string>
    <string name="theme">Theme</string>
    <string name="time_format_24h">HH:mm</string>
    <string name="unmute">Unmute</string>
    <string name="user_agent">User Agent</string>
    <string name="version">Version</string>
    <string name="volume">Volume</string>
    <string name="welcome_subtitle">Your premium IPTV experience</string>
    <string name="welcome_title">Welcome to BadBoyz IPTV</string>
    <string name="yes">Yes</string>
    <style name="BottomNavigation.Primary" parent="Widget.Material3.BottomNavigationView">
        <item name="android:background">@color/background_secondary</item>
        <item name="itemIconTint">@color/bottom_nav_color</item>
        <item name="itemTextColor">@color/bottom_nav_color</item>
        <item name="android:elevation">8dp</item>
    </style>
    <style name="Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>
    <style name="Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/primary</item>
        <item name="android:textColor">@color/primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>
    <style name="CardView.Channel" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="strokeColor">@color/card_stroke</item>
        <item name="strokeWidth">1dp</item>
    </style>
    <style name="CardView.Playlist" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">6dp</item>
        <item name="strokeColor">@color/card_stroke</item>
        <item name="strokeWidth">1dp</item>
    </style>
    <style name="TextAppearance.Body" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>
    <style name="TextAppearance.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>
    <style name="TextAppearance.Subtitle" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>
    <style name="TextAppearance.Title" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>
    <style name="TextInputLayout.Outlined" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>
    <style name="Theme.BadBoyzIPTVPlayer" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        
        
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        
        <item name="android:statusBarColor">@color/background_primary</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/background_primary</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:windowBackground">@color/background_primary</item>
        <item name="android:windowAnimationStyle">@style/WindowAnimationTransition</item>
    </style>
    <style name="Theme.BadBoyzIPTVPlayer.Fullscreen" parent="Theme.BadBoyzIPTVPlayer.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@color/player_background</item>
        <item name="android:keepScreenOn">true</item>
    </style>
    <style name="Theme.BadBoyzIPTVPlayer.NoActionBar" parent="Theme.BadBoyzIPTVPlayer">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.BadBoyzIPTVPlayer.Splash" parent="Theme.BadBoyzIPTVPlayer.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="Toolbar.Primary" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/background_secondary</item>
        <item name="titleTextColor">@color/text_primary</item>
        <item name="subtitleTextColor">@color/text_secondary</item>
        <item name="android:elevation">4dp</item>
    </style>
    <style name="WindowAnimationTransition">
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_left</item>
    </style>
</resources>