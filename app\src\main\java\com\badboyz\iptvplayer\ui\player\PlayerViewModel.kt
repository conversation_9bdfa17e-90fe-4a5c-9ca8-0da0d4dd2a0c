package com.badboyz.iptvplayer.ui.player

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.badboyz.iptvplayer.data.model.EPGProgram
import com.badboyz.iptvplayer.data.repository.ChannelRepository
import com.badboyz.iptvplayer.data.repository.EPGRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PlayerViewModel @Inject constructor(
    private val channelRepository: ChannelRepository,
    private val epgRepository: EPGRepository
) : ViewModel() {
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    private val _currentProgram = MutableLiveData<EPGProgram?>()
    val currentProgram: LiveData<EPGProgram?> = _currentProgram
    
    private val _nextProgram = MutableLiveData<EPGProgram?>()
    val nextProgram: LiveData<EPGProgram?> = _nextProgram
    
    fun loadEPGData(channelId: String) {
        viewModelScope.launch {
            try {
                val current = epgRepository.getCurrentProgram(channelId)
                val next = epgRepository.getNextProgram(channelId)
                
                _currentProgram.value = current
                _nextProgram.value = next
            } catch (e: Exception) {
                _error.value = e.message
            }
        }
    }
    
    fun updateWatchHistory(channelId: Long) {
        viewModelScope.launch {
            try {
                channelRepository.updateWatchHistory(channelId)
            } catch (e: Exception) {
                // Silently handle error for watch history
            }
        }
    }
    
    fun setLoading(loading: Boolean) {
        _isLoading.value = loading
    }
    
    fun setError(message: String?) {
        _error.value = message
    }
    
    fun clearError() {
        _error.value = null
    }
}
