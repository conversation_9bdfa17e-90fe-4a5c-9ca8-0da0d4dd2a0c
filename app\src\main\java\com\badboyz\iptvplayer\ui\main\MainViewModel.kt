package com.badboyz.iptvplayer.ui.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.badboyz.iptvplayer.data.model.Channel
import com.badboyz.iptvplayer.data.model.Playlist
import com.badboyz.iptvplayer.data.repository.ChannelRepository
import com.badboyz.iptvplayer.data.repository.PlaylistRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val playlistRepository: PlaylistRepository,
    private val channelRepository: ChannelRepository
) : ViewModel() {
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    private val _playlists = MutableLiveData<List<Playlist>>()
    val playlists: LiveData<List<Playlist>> = _playlists
    
    private val _recentlyWatchedChannels = MutableLiveData<List<Channel>>()
    val recentlyWatchedChannels: LiveData<List<Channel>> = _recentlyWatchedChannels
    
    private val _favoriteChannels = MutableLiveData<List<Channel>>()
    val favoriteChannels: LiveData<List<Channel>> = _favoriteChannels
    
    init {
        loadData()
    }
    
    private fun loadData() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // Load playlists
                playlistRepository.getAllPlaylistsLiveData().observeForever { playlists ->
                    _playlists.value = playlists
                }
                
                // Load recently watched channels
                channelRepository.getRecentlyWatchedChannels(10).collect { channels ->
                    _recentlyWatchedChannels.value = channels
                }
                
                // Load favorite channels
                channelRepository.getFavoriteChannelsLiveData().observeForever { channels ->
                    _favoriteChannels.value = channels
                }
                
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun addPlaylist(name: String, url: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val result = playlistRepository.importPlaylistFromUrl(name, url)
                result.fold(
                    onSuccess = {
                        _error.value = null
                        // Playlist added successfully
                    },
                    onFailure = { exception ->
                        _error.value = exception.message
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun refreshPlaylists() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val playlists = _playlists.value ?: emptyList()
                for (playlist in playlists) {
                    playlistRepository.refreshPlaylist(playlist.id)
                }
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deletePlaylist(playlistId: Long) {
        viewModelScope.launch {
            try {
                val result = playlistRepository.deletePlaylist(playlistId)
                result.fold(
                    onSuccess = {
                        _error.value = null
                    },
                    onFailure = { exception ->
                        _error.value = exception.message
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message
            }
        }
    }
    
    fun toggleChannelFavorite(channelId: Long) {
        viewModelScope.launch {
            try {
                val result = channelRepository.toggleFavorite(channelId)
                result.fold(
                    onSuccess = {
                        _error.value = null
                    },
                    onFailure = { exception ->
                        _error.value = exception.message
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message
            }
        }
    }
    
    fun updateChannelWatchHistory(channelId: Long) {
        viewModelScope.launch {
            try {
                channelRepository.updateWatchHistory(channelId)
            } catch (e: Exception) {
                _error.value = e.message
            }
        }
    }
    
    fun clearError() {
        _error.value = null
    }
}
