# BadBoyz IPTV Player

A modern, attractive, and functional IPTV player app for Android built with Android Studio. This app provides a premium IPTV viewing experience with a sleek design inspired by popular IPTV applications like iMPlayer.

## Features

### 🎯 Core Features
- **M3U Playlist Support**: Import and manage M3U/M3U8 playlists from URLs
- **Smooth Video Playback**: Powered by ExoPlayer for optimal streaming performance
- **Modern UI/UX**: Material Design 3 with dark theme and attractive animations
- **Channel Management**: Organize channels by groups, favorites, and recently watched
- **Search Functionality**: Quick search across all channels
- **EPG Support**: Electronic Program Guide integration (ready for implementation)

### 📱 User Interface
- **Home Screen**: Quick access to playlists, favorites, and recently watched channels
- **Navigation Drawer**: Easy navigation between different sections
- **Bottom Navigation**: Quick access to main features
- **Floating Action Button**: Quick playlist addition
- **Card-based Design**: Modern card layouts for better visual hierarchy

### 🎥 Player Features
- **Fullscreen Playback**: Landscape orientation with immersive controls
- **Custom Controls**: Beautiful custom ExoPlayer controls
- **Multiple Formats**: Support for HLS, DASH, and other streaming formats
- **Buffering Indicators**: Visual feedback for loading states
- **Error Handling**: Graceful error handling with user-friendly messages

### 🗄️ Data Management
- **Room Database**: Local storage for playlists, channels, and user preferences
- **MVVM Architecture**: Clean architecture with ViewModels and LiveData
- **Repository Pattern**: Centralized data management
- **Dependency Injection**: Hilt for clean dependency management

## Technical Stack

### Architecture
- **MVVM (Model-View-ViewModel)**: Clean separation of concerns
- **Repository Pattern**: Centralized data access
- **Dependency Injection**: Hilt for DI
- **Navigation Component**: Type-safe navigation

### Libraries & Technologies
- **Kotlin**: Primary programming language
- **Android Jetpack**: Modern Android development components
- **ExoPlayer**: Video playback engine
- **Room**: Local database
- **Retrofit**: Network requests
- **Glide**: Image loading
- **Material Design 3**: UI components
- **Coroutines**: Asynchronous programming
- **LiveData & Flow**: Reactive data streams

### UI/UX
- **Material Design 3**: Latest design system
- **Dark Theme**: Eye-friendly dark interface
- **Custom Animations**: Smooth transitions and interactions
- **Responsive Design**: Optimized for different screen sizes
- **Accessibility**: Content descriptions and proper navigation

## Project Structure

```
app/
├── src/main/java/com/badboyz/iptvplayer/
│   ├── data/
│   │   ├── database/          # Room database, DAOs
│   │   ├── model/             # Data models
│   │   ├── network/           # Network services
│   │   ├── parser/            # M3U parser
│   │   └── repository/        # Repository implementations
│   ├── di/                    # Dependency injection modules
│   ├── ui/
│   │   ├── adapters/          # RecyclerView adapters
│   │   ├── dialogs/           # Dialog fragments
│   │   ├── home/              # Home screen
│   │   ├── main/              # Main activity
│   │   ├── player/            # Video player
│   │   └── ...                # Other UI components
│   └── IPTVApplication.kt     # Application class
└── src/main/res/
    ├── drawable/              # Vector drawables, backgrounds
    ├── layout/                # XML layouts
    ├── menu/                  # Menu resources
    ├── navigation/            # Navigation graph
    ├── values/                # Colors, strings, themes
    └── ...
```

## Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24 (API level 24) or higher
- Kotlin 1.9.10 or later

### Installation
1. Clone the repository
2. Open the project in Android Studio
3. Sync the project with Gradle files
4. Build and run the app

### Adding Playlists
1. Tap the "+" floating action button or "Add Playlist" card
2. Enter a name for your playlist
3. Enter the M3U playlist URL
4. Tap "Save" to import the playlist

## Design Inspiration

The app's design is inspired by modern IPTV applications like iMPlayer, featuring:
- **Dark Theme**: Reduces eye strain during extended viewing
- **Card-based Layout**: Clean, organized content presentation
- **Gradient Accents**: Beautiful color gradients for visual appeal
- **Smooth Animations**: Fluid transitions between screens
- **Intuitive Navigation**: Easy-to-use navigation patterns

## Color Scheme

- **Primary**: Blue (#1E88E5) - Modern and trustworthy
- **Accent**: Orange (#FF5722) - Energetic and attention-grabbing
- **Background**: Dark grays for comfortable viewing
- **Text**: High contrast whites and grays for readability

## Future Enhancements

- [ ] EPG (Electronic Program Guide) integration
- [ ] Chromecast support
- [ ] Picture-in-Picture mode
- [ ] Parental controls
- [ ] Multiple playlist management
- [ ] Cloud sync for favorites
- [ ] Advanced search filters
- [ ] Recording functionality
- [ ] Subtitle support
- [ ] Audio track selection

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- ExoPlayer team for the excellent media player library
- Material Design team for the beautiful design system
- Android Jetpack team for modern development tools
- iMPlayer and other IPTV apps for design inspiration
