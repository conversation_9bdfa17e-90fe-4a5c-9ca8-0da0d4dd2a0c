package com.badboyz.iptvplayer.di

import com.badboyz.iptvplayer.data.database.IPTVDatabase
import com.badboyz.iptvplayer.data.database.dao.ChannelDao
import com.badboyz.iptvplayer.data.database.dao.EPGDao
import com.badboyz.iptvplayer.data.database.dao.PlaylistDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideIPTVDatabase(): IPTVDatabase {
        return IPTVDatabase.getDatabase()
    }
    
    @Provides
    fun provideChannelDao(database: IPTVDatabase): ChannelDao {
        return database.channelDao()
    }
    
    @Provides
    fun providePlaylistDao(database: IPTVDatabase): PlaylistDao {
        return database.playlistDao()
    }
    
    @Provides
    fun provideEPGDao(database: IPTVDatabase): EPGDao {
        return database.epgDao()
    }
}
