<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.badboyz.iptvplayer" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="86" endOffset="43"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="16" startOffset="8" endLine="34" endOffset="57"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="24" startOffset="12" endLine="32" endOffset="44"/></Target><Target id="@+id/nav_host_fragment" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="37" startOffset="8" endLine="44" endOffset="50"/></Target><Target id="@+id/bottom_navigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="47" startOffset="8" endLine="56" endOffset="46"/></Target><Target id="@+id/fab_add_playlist" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="59" startOffset="8" endLine="69" endOffset="37"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="74" startOffset="4" endLine="84" endOffset="49"/></Target></Targets></Layout>