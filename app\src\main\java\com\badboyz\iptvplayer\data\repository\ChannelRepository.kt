package com.badboyz.iptvplayer.data.repository

import androidx.lifecycle.LiveData
import com.badboyz.iptvplayer.data.database.dao.ChannelDao
import com.badboyz.iptvplayer.data.model.Channel
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ChannelRepository @Inject constructor(
    private val channelDao: ChannelDao
) {
    
    fun getChannelsByPlaylist(playlistId: Long): Flow<List<Channel>> = 
        channelDao.getChannelsByPlaylist(playlistId)
    
    fun getChannelsByPlaylistLiveData(playlistId: Long): LiveData<List<Channel>> = 
        channelDao.getChannelsByPlaylistLiveData(playlistId)
    
    fun getFavoriteChannels(): Flow<List<Channel>> = channelDao.getFavoriteChannels()
    
    fun getFavoriteChannelsLiveData(): LiveData<List<Channel>> = channelDao.getFavoriteChannelsLiveData()
    
    fun searchChannels(query: String): Flow<List<Channel>> = channelDao.searchChannels(query)
    
    fun getGroupsByPlaylist(playlistId: Long): Flow<List<String>> = 
        channelDao.getGroupsByPlaylist(playlistId)
    
    fun getChannelsByGroup(playlistId: Long, group: String): Flow<List<Channel>> = 
        channelDao.getChannelsByGroup(playlistId, group)
    
    suspend fun getChannelById(channelId: Long): Channel? = channelDao.getChannelById(channelId)
    
    fun getRecentlyWatchedChannels(limit: Int = 10): Flow<List<Channel>> = 
        channelDao.getRecentlyWatchedChannels(limit)
    
    fun getMostWatchedChannels(limit: Int = 10): Flow<List<Channel>> = 
        channelDao.getMostWatchedChannels(limit)
    
    suspend fun updateChannel(channel: Channel): Result<Unit> {
        return try {
            channelDao.updateChannel(channel)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun toggleFavorite(channelId: Long): Result<Unit> {
        return try {
            val channel = channelDao.getChannelById(channelId)
            if (channel != null) {
                channelDao.updateFavoriteStatus(channelId, !channel.isFavorite)
                Result.success(Unit)
            } else {
                Result.failure(Exception("Channel not found"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updateWatchHistory(channelId: Long): Result<Unit> {
        return try {
            channelDao.updateWatchHistory(channelId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun deleteChannel(channel: Channel): Result<Unit> {
        return try {
            channelDao.deleteChannel(channel)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getChannelCountByPlaylist(playlistId: Long): Int = 
        channelDao.getChannelCountByPlaylist(playlistId)
}
