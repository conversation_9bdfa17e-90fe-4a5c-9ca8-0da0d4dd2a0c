1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.badboyz.iptvplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="31" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- Storage permissions -->
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:11:5-80
17-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:11:22-77
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:12:5-81
18-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:12:22-78
19
20    <!-- Wake lock for video playback -->
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:15:5-68
21-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:15:22-65
22
23    <!-- Audio focus -->
24    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
24-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:18:5-80
24-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:18:22-77
25
26    <permission
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
27        android:name="com.badboyz.iptvplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.badboyz.iptvplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
31
32    <application
32-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:20:5-73:19
33        android:name="com.badboyz.iptvplayer.IPTVApplication"
33-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:21:9-40
34        android:allowBackup="true"
34-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:22:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\55604ab945968ea65ece63dd7ad46a32\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
36        android:dataExtractionRules="@xml/data_extraction_rules"
36-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:23:9-65
37        android:debuggable="true"
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:24:9-54
40        android:icon="@mipmap/ic_launcher"
40-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:25:9-43
41        android:label="@string/app_name"
41-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:26:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:27:9-54
43        android:supportsRtl="true"
43-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:28:9-35
44        android:theme="@style/Theme.BadBoyzIPTVPlayer"
44-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:29:9-55
45        android:usesCleartextTraffic="true" >
45-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:30:9-44
46
47        <!-- Main Activity -->
48        <activity
48-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:34:9-43:20
49            android:name="com.badboyz.iptvplayer.ui.main.MainActivity"
49-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:35:13-49
50            android:exported="true"
50-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:36:13-36
51            android:screenOrientation="portrait"
51-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:37:13-49
52            android:theme="@style/Theme.BadBoyzIPTVPlayer.NoActionBar" >
52-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:38:13-71
53            <intent-filter>
53-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:39:13-42:29
54                <action android:name="android.intent.action.MAIN" />
54-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:40:17-69
54-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:40:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:41:17-77
56-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:41:27-74
57            </intent-filter>
58        </activity>
59
60        <!-- Player Activity -->
61        <activity
61-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:46:9-51:73
62            android:name="com.badboyz.iptvplayer.ui.player.PlayerActivity"
62-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:47:13-53
63            android:configChanges="orientation|screenSize|keyboardHidden"
63-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:49:13-74
64            android:exported="false"
64-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:48:13-37
65            android:screenOrientation="landscape"
65-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:50:13-50
66            android:theme="@style/Theme.BadBoyzIPTVPlayer.Fullscreen" />
66-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:51:13-70
67
68        <!-- Splash Activity -->
69        <activity
69-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:54:9-65:20
70            android:name="com.badboyz.iptvplayer.ui.splash.SplashActivity"
70-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:55:13-53
71            android:exported="true"
71-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:56:13-36
72            android:theme="@style/Theme.BadBoyzIPTVPlayer.Splash" >
72-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:57:13-66
73            <intent-filter>
73-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:58:13-64:29
74                <action android:name="android.intent.action.VIEW" />
74-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:59:17-69
74-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:59:25-66
75
76                <category android:name="android.intent.category.DEFAULT" />
76-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:60:17-76
76-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:60:27-73
77                <category android:name="android.intent.category.BROWSABLE" />
77-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:61:17-78
77-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:61:27-75
78
79                <data
79-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:17-95
80                    android:host="*"
80-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:45-61
81                    android:pathPattern=".*\\.m3u"
81-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:62-92
82                    android:scheme="http" />
82-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:23-44
83                <data
83-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:17-95
84                    android:host="*"
84-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:45-61
85                    android:pathPattern=".*\\.m3u"
85-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:62-92
86                    android:scheme="https" />
86-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:62:23-44
87            </intent-filter>
88        </activity>
89
90        <!-- Settings Activity -->
91        <activity
91-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:68:9-71:66
92            android:name="com.badboyz.iptvplayer.ui.settings.SettingsActivity"
92-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:69:13-57
93            android:exported="false"
93-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:70:13-37
94            android:parentActivityName="com.badboyz.iptvplayer.ui.main.MainActivity" />
94-->C:\Users\<USER>\Desktop\badboyz app\app\src\main\AndroidManifest.xml:71:13-63
95        <activity
95-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
96            android:name="com.karumi.dexter.DexterActivity"
96-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
97            android:theme="@style/Dexter.Internal.Theme.Transparent" />
97-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-4\47def88bf2021763cc64c5dd2775191d\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
98        <activity
98-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
99            android:name="androidx.compose.ui.tooling.PreviewActivity"
99-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
100            android:exported="true" />
100-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae994abbbf54bb1bf658eb40423b9696\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
101        <activity
101-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:23:9-25:39
102            android:name="androidx.activity.ComponentActivity"
102-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:24:13-63
103            android:exported="true" />
103-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e1a59aaa9ae52792441e1bdc2816bd1\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:25:13-36
104
105        <provider
105-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
106            android:name="androidx.startup.InitializationProvider"
106-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
107            android:authorities="com.badboyz.iptvplayer.androidx-startup"
107-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
108            android:exported="false" >
108-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
109            <meta-data
109-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
110                android:name="androidx.emoji2.text.EmojiCompatInitializer"
110-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
111                android:value="androidx.startup" />
111-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\52eccb6893f36c1d34969d73733a33cd\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
112            <meta-data
112-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
113                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
114                android:value="androidx.startup" />
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\57a6f84199b83723fdbe4a2fa4e7b550\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
115            <meta-data
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
117                android:value="androidx.startup" />
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
118        </provider>
119
120        <uses-library
120-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
121            android:name="androidx.window.extensions"
121-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
122            android:required="false" />
122-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
123        <uses-library
123-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
124            android:name="androidx.window.sidecar"
124-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
125            android:required="false" />
125-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\142130077e524bdebac8d2bf1464fc54\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
126
127        <service
127-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
128            android:name="androidx.room.MultiInstanceInvalidationService"
128-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
129            android:directBootAware="true"
129-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
130            android:exported="false" />
130-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2f5e468d327772e5ca29f4e4ab49c6c2\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
131
132        <receiver
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
133            android:name="androidx.profileinstaller.ProfileInstallReceiver"
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
134            android:directBootAware="false"
134-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
135            android:enabled="true"
135-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
136            android:exported="true"
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
137            android:permission="android.permission.DUMP" >
137-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
139                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
142                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
145                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
145-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
145-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
146            </intent-filter>
147            <intent-filter>
147-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
148                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
148-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
148-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab49d21ea0337b6e5c440ba268f81bdf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
149            </intent-filter>
150        </receiver>
151    </application>
152
153</manifest>
