package com.badboyz.iptvplayer.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "epg_programs")
data class EPGProgram(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val channelId: String,
    val title: String,
    val description: String? = null,
    val startTime: Long,
    val endTime: Long,
    val category: String? = null,
    val icon: String? = null,
    val rating: String? = null,
    val year: Int? = null,
    val director: String? = null,
    val actors: String? = null,
    val country: String? = null,
    val language: String? = null,
    val isLive: Boolean = false,
    val isPremiere: Boolean = false,
    val isRepeat: Boolean = false
) {
    
    fun isCurrentlyPlaying(): Boolean {
        val now = System.currentTimeMillis()
        return now in startTime..endTime
    }
    
    fun getDurationMinutes(): Int {
        return ((endTime - startTime) / (1000 * 60)).toInt()
    }
    
    fun getProgressPercentage(): Float {
        val now = System.currentTimeMillis()
        if (now < startTime) return 0f
        if (now > endTime) return 100f
        
        val totalDuration = endTime - startTime
        val elapsed = now - startTime
        return (elapsed.toFloat() / totalDuration.toFloat()) * 100f
    }
}
