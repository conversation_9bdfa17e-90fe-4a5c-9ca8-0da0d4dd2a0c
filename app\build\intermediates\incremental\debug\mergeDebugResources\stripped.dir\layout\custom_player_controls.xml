<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="bottom"
    android:background="@drawable/controls_overlay_background"
    android:orientation="vertical"
    android:paddingLeft="16dp"
    android:paddingTop="16dp"
    android:paddingRight="16dp"
    android:paddingBottom="16dp">

    <!-- Spacer to push controls to bottom -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Progress Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!-- Current Time -->
        <TextView
            android:id="@id/exo_position"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:includeFontPadding="false" />

        <!-- Progress Bar -->
        <androidx.media3.ui.DefaultTimeBar
            android:id="@id/exo_progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:played_color="@color/primary"
            app:buffered_color="@color/player_progress_background"
            app:unplayed_color="@color/player_progress_background"
            app:scrubber_color="@color/primary"
            app:bar_height="4dp"
            app:touch_target_height="26dp" />

        <!-- Duration -->
        <TextView
            android:id="@id/exo_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:includeFontPadding="false" />

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:orientation="horizontal">

        <!-- Previous Button -->
        <ImageButton
            android:id="@id/exo_prev"
            style="@style/ExoMediaButton.Previous"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/exo_controls_previous"
            android:tint="@color/white"
            android:contentDescription="@string/exo_controls_previous_description" />

        <!-- Rewind Button -->
        <ImageButton
            android:id="@id/exo_rew"
            style="@style/ExoMediaButton.Rewind"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/exo_controls_rewind"
            android:tint="@color/white"
            android:contentDescription="@string/exo_controls_rewind_description" />

        <!-- Play/Pause Button -->
        <ImageButton
            android:id="@id/exo_play"
            style="@style/ExoMediaButton.Play"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/exo_controls_play"
            android:tint="@color/white"
            android:contentDescription="@string/exo_controls_play_description" />

        <ImageButton
            android:id="@id/exo_pause"
            style="@style/ExoMediaButton.Pause"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/exo_controls_pause"
            android:tint="@color/white"
            android:contentDescription="@string/exo_controls_pause_description" />

        <!-- Fast Forward Button -->
        <ImageButton
            android:id="@id/exo_ffwd"
            style="@style/ExoMediaButton.FastForward"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/exo_controls_fastforward"
            android:tint="@color/white"
            android:contentDescription="@string/exo_controls_fastforward_description" />

        <!-- Next Button -->
        <ImageButton
            android:id="@id/exo_next"
            style="@style/ExoMediaButton.Next"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/exo_controls_next"
            android:tint="@color/white"
            android:contentDescription="@string/exo_controls_next_description" />

    </LinearLayout>

</LinearLayout>
