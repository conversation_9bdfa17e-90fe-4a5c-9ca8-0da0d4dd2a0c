package com.badboyz.iptvplayer.data.database.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.badboyz.iptvplayer.data.model.Playlist
import kotlinx.coroutines.flow.Flow

@Dao
interface PlaylistDao {
    
    @Query("SELECT * FROM playlists ORDER BY sortOrder ASC, name ASC")
    fun getAllPlaylists(): Flow<List<Playlist>>
    
    @Query("SELECT * FROM playlists ORDER BY sortOrder ASC, name ASC")
    fun getAllPlaylistsLiveData(): LiveData<List<Playlist>>
    
    @Query("SELECT * FROM playlists WHERE isActive = 1 ORDER BY sortOrder ASC, name ASC")
    fun getActivePlaylists(): Flow<List<Playlist>>
    
    @Query("SELECT * FROM playlists WHERE id = :playlistId")
    suspend fun getPlaylistById(playlistId: Long): Playlist?
    
    @Query("SELECT * FROM playlists WHERE url = :url LIMIT 1")
    suspend fun getPlaylistByUrl(url: String): Playlist?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlaylist(playlist: Playlist): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlaylists(playlists: List<Playlist>)
    
    @Update
    suspend fun updatePlaylist(playlist: Playlist)
    
    @Query("UPDATE playlists SET channelCount = :count WHERE id = :playlistId")
    suspend fun updateChannelCount(playlistId: Long, count: Int)
    
    @Query("UPDATE playlists SET lastRefresh = :timestamp WHERE id = :playlistId")
    suspend fun updateLastRefresh(playlistId: Long, timestamp: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deletePlaylist(playlist: Playlist)
    
    @Query("DELETE FROM playlists WHERE id = :playlistId")
    suspend fun deletePlaylistById(playlistId: Long)
    
    @Query("DELETE FROM playlists")
    suspend fun deleteAllPlaylists()
    
    @Query("SELECT COUNT(*) FROM playlists")
    suspend fun getPlaylistCount(): Int
}
