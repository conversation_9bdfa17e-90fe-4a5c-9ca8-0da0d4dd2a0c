package com.badboyz.iptvplayer.ui.player

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.badboyz.iptvplayer.data.model.Channel
import com.badboyz.iptvplayer.databinding.ActivityPlayerBinding
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.hls.HlsMediaSource
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.common.util.Util
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PlayerActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityPlayerBinding
    private val viewModel: PlayerViewModel by viewModels()
    
    private var player: ExoPlayer? = null
    private var playWhenReady = true
    private var currentItem = 0
    private var playbackPosition = 0L
    
    private lateinit var channel: Channel
    
    companion object {
        private const val EXTRA_CHANNEL = "extra_channel"
        
        fun createIntent(context: Context, channel: Channel): Intent {
            return Intent(context, PlayerActivity::class.java).apply {
                putExtra(EXTRA_CHANNEL, channel)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPlayerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Get channel from intent
        channel = intent.getParcelableExtra(EXTRA_CHANNEL) ?: run {
            finish()
            return
        }
        
        setupFullscreen()
        setupUI()
        observeViewModel()
        
        // Update watch history
        viewModel.updateWatchHistory(channel.id)
    }
    
    private fun setupFullscreen() {
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowInsetsControllerCompat(window, binding.root).let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
        
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
    
    private fun setupUI() {
        binding.tvChannelName.text = channel.getDisplayName()
        binding.tvChannelGroup.text = channel.getDisplayGroup()
        
        // Setup controls
        binding.btnBack.setOnClickListener {
            finish()
        }
        
        binding.btnFullscreen.setOnClickListener {
            toggleFullscreen()
        }
        
        // Hide controls after delay
        binding.playerView.setControllerVisibilityListener { visibility ->
            binding.controlsOverlay.visibility = visibility
        }
    }
    
    private fun observeViewModel() {
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
        
        viewModel.error.observe(this) { error ->
            error?.let {
                binding.tvError.text = it
                binding.tvError.visibility = View.VISIBLE
            }
        }
        
        viewModel.currentProgram.observe(this) { program ->
            program?.let {
                binding.tvNowPlaying.text = "Now: ${it.title}"
                binding.tvNowPlaying.visibility = View.VISIBLE
            }
        }
        
        viewModel.nextProgram.observe(this) { program ->
            program?.let {
                binding.tvNextProgram.text = "Next: ${it.title}"
                binding.tvNextProgram.visibility = View.VISIBLE
            }
        }
    }
    
    private fun initializePlayer() {
        player = ExoPlayer.Builder(this)
            .build()
            .also { exoPlayer ->
                binding.playerView.player = exoPlayer
                
                val mediaItem = MediaItem.fromUri(channel.url)
                exoPlayer.setMediaItem(mediaItem, false)
                exoPlayer.playWhenReady = playWhenReady
                exoPlayer.seekTo(currentItem, playbackPosition)
                exoPlayer.prepare()
                
                exoPlayer.addListener(object : Player.Listener {
                    override fun onPlayerError(error: PlaybackException) {
                        viewModel.setError("Playback error: ${error.message}")
                    }
                    
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        when (playbackState) {
                            Player.STATE_BUFFERING -> {
                                viewModel.setLoading(true)
                            }
                            Player.STATE_READY -> {
                                viewModel.setLoading(false)
                                binding.tvError.visibility = View.GONE
                            }
                            Player.STATE_ENDED -> {
                                // Handle stream end
                            }
                        }
                    }
                })
            }
    }
    
    private fun releasePlayer() {
        player?.let { exoPlayer ->
            playbackPosition = exoPlayer.currentPosition
            currentItem = exoPlayer.currentMediaItemIndex
            playWhenReady = exoPlayer.playWhenReady
            exoPlayer.release()
        }
        player = null
    }
    
    private fun toggleFullscreen() {
        // Already in fullscreen, this could toggle UI visibility
        val controller = WindowInsetsControllerCompat(window, binding.root)
        if (binding.controlsOverlay.visibility == View.VISIBLE) {
            controller.hide(WindowInsetsCompat.Type.systemBars())
            binding.controlsOverlay.visibility = View.GONE
        } else {
            controller.show(WindowInsetsCompat.Type.systemBars())
            binding.controlsOverlay.visibility = View.VISIBLE
        }
    }
    
    override fun onStart() {
        super.onStart()
        if (Util.SDK_INT > 23) {
            initializePlayer()
        }
    }
    
    override fun onResume() {
        super.onResume()
        if (Util.SDK_INT <= 23 || player == null) {
            initializePlayer()
        }
    }
    
    override fun onPause() {
        super.onPause()
        if (Util.SDK_INT <= 23) {
            releasePlayer()
        }
    }
    
    override fun onStop() {
        super.onStop()
        if (Util.SDK_INT > 23) {
            releasePlayer()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }
}
