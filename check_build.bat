@echo off
echo Checking BadBoyz IPTV Player build status...
echo.

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ BUILD COMPLETE! APK found.
    echo Location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo You can now run: .\install_app.bat
    echo Or manually install the APK to your device.
) else (
    echo ❌ Build not complete yet.
    echo The APK has not been generated.
    echo.
    echo Please wait for the build to finish, or try Android Studio.
)

echo.
pause
