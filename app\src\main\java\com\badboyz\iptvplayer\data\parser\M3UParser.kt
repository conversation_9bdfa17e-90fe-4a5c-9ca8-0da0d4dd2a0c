package com.badboyz.iptvplayer.data.parser

import com.badboyz.iptvplayer.data.model.Channel
import java.io.BufferedReader
import java.io.StringReader
import java.util.regex.Pattern

class M3UParser {
    
    companion object {
        private val EXTINF_PATTERN = Pattern.compile("#EXTINF:(-?\\d+)\\s*(.*),\\s*(.*)")
        private val TVG_ID_PATTERN = Pattern.compile("tvg-id=\"([^\"]*)")
        private val TVG_NAME_PATTERN = Pattern.compile("tvg-name=\"([^\"]*)")
        private val TVG_LOGO_PATTERN = Pattern.compile("tvg-logo=\"([^\"]*)")
        private val GROUP_TITLE_PATTERN = Pattern.compile("group-title=\"([^\"]*)")
        private val USER_AGENT_PATTERN = Pattern.compile("user-agent=\"([^\"]*)")
        private val REFERER_PATTERN = Pattern.compile("referer=\"([^\"]*)")
    }
    
    fun parseM3U(content: String, playlistId: Long): List<Channel> {
        val channels = mutableListOf<Channel>()
        val reader = BufferedReader(StringReader(content))
        
        var line: String?
        var currentExtinf: String? = null
        var sortOrder = 0
        
        while (reader.readLine().also { line = it } != null) {
            line?.let { currentLine ->
                when {
                    currentLine.startsWith("#EXTINF:") -> {
                        currentExtinf = currentLine
                    }
                    currentLine.startsWith("http") || currentLine.startsWith("rtmp") || 
                    currentLine.startsWith("rtsp") || currentLine.startsWith("udp") -> {
                        currentExtinf?.let { extinf ->
                            val channel = parseChannel(extinf, currentLine, playlistId, sortOrder++)
                            if (channel != null) {
                                channels.add(channel)
                            }
                        }
                        currentExtinf = null
                    }
                }
            }
        }
        
        return channels
    }
    
    private fun parseChannel(extinf: String, url: String, playlistId: Long, sortOrder: Int): Channel? {
        val matcher = EXTINF_PATTERN.matcher(extinf)
        if (!matcher.find()) return null
        
        val duration = matcher.group(1)?.toIntOrNull() ?: -1
        val attributes = matcher.group(2) ?: ""
        val title = matcher.group(3)?.trim() ?: "Unknown Channel"
        
        return Channel(
            name = title,
            url = url.trim(),
            tvgId = extractAttribute(attributes, TVG_ID_PATTERN),
            tvgName = extractAttribute(attributes, TVG_NAME_PATTERN),
            tvgLogo = extractAttribute(attributes, TVG_LOGO_PATTERN),
            groupTitle = extractAttribute(attributes, GROUP_TITLE_PATTERN),
            group = extractAttribute(attributes, GROUP_TITLE_PATTERN),
            userAgent = extractAttribute(attributes, USER_AGENT_PATTERN),
            referer = extractAttribute(attributes, REFERER_PATTERN),
            playlistId = playlistId,
            sortOrder = sortOrder
        )
    }
    
    private fun extractAttribute(attributes: String, pattern: Pattern): String? {
        val matcher = pattern.matcher(attributes)
        return if (matcher.find()) {
            matcher.group(1)?.takeIf { it.isNotBlank() }
        } else null
    }
    
    fun validateM3U(content: String): Boolean {
        return content.trim().startsWith("#EXTM3U") && 
               content.contains("#EXTINF:") && 
               (content.contains("http") || content.contains("rtmp") || 
                content.contains("rtsp") || content.contains("udp"))
    }
    
    fun extractPlaylistInfo(content: String): Map<String, String> {
        val info = mutableMapOf<String, String>()
        val lines = content.lines()
        
        for (line in lines) {
            when {
                line.startsWith("#EXTM3U") -> {
                    // Extract playlist-level attributes if any
                    if (line.contains("url-tvg=")) {
                        val tvgUrl = extractQuotedValue(line, "url-tvg")
                        if (tvgUrl != null) info["tvg-url"] = tvgUrl
                    }
                }
                line.startsWith("#PLAYLIST:") -> {
                    info["name"] = line.substring(10).trim()
                }
            }
        }
        
        return info
    }
    
    private fun extractQuotedValue(line: String, attribute: String): String? {
        val pattern = Pattern.compile("$attribute=\"([^\"]*)")
        val matcher = pattern.matcher(line)
        return if (matcher.find()) matcher.group(1) else null
    }
}
