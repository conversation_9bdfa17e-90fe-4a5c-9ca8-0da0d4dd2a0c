package com.badboyz.iptvplayer.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.badboyz.iptvplayer.R
import com.badboyz.iptvplayer.databinding.FragmentHomeBinding
import com.badboyz.iptvplayer.ui.adapters.ChannelAdapter
import com.badboyz.iptvplayer.ui.adapters.PlaylistAdapter
import com.badboyz.iptvplayer.ui.dialogs.AddPlaylistDialog
import com.badboyz.iptvplayer.ui.main.MainViewModel
import com.badboyz.iptvplayer.ui.player.PlayerActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeFragment : Fragment() {
    
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MainViewModel by viewModels()
    
    private lateinit var playlistAdapter: PlaylistAdapter
    private lateinit var recentChannelsAdapter: ChannelAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerViews()
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupRecyclerViews() {
        // Setup playlists RecyclerView
        playlistAdapter = PlaylistAdapter(
            onPlaylistClick = { playlist ->
                // Navigate to channels fragment
                val action = HomeFragmentDirections.actionNavHomeToNavChannels(playlist.id)
                findNavController().navigate(action)
            },
            onPlaylistMenuClick = { playlist ->
                // Show playlist menu
                showPlaylistMenu(playlist)
            }
        )
        
        binding.rvPlaylists.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = playlistAdapter
        }
        
        // Setup recently watched channels RecyclerView
        recentChannelsAdapter = ChannelAdapter(
            onChannelClick = { channel ->
                // Start player activity
                val intent = PlayerActivity.createIntent(requireContext(), channel)
                startActivity(intent)
            },
            onFavoriteClick = { channel ->
                viewModel.toggleChannelFavorite(channel.id)
            }
        )
        
        binding.rvRecentlyWatched.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = recentChannelsAdapter
        }
    }
    
    private fun setupClickListeners() {
        binding.cardAddPlaylist.setOnClickListener {
            showAddPlaylistDialog()
        }
        
        binding.cardFavorites.setOnClickListener {
            findNavController().navigate(R.id.nav_favorites)
        }
    }
    
    private fun observeViewModel() {
        viewModel.playlists.observe(viewLifecycleOwner) { playlists ->
            playlistAdapter.submitList(playlists)
        }
        
        viewModel.recentlyWatchedChannels.observe(viewLifecycleOwner) { channels ->
            recentChannelsAdapter.submitList(channels)
            binding.rvRecentlyWatched.visibility = if (channels.isEmpty()) View.GONE else View.VISIBLE
        }
        
        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                // Show error message
                // TODO: Implement proper error handling
            }
        }
    }
    
    private fun showAddPlaylistDialog() {
        val dialog = AddPlaylistDialog { name, url ->
            viewModel.addPlaylist(name, url)
        }
        dialog.show(parentFragmentManager, "AddPlaylistDialog")
    }
    
    private fun showPlaylistMenu(playlist: com.badboyz.iptvplayer.data.model.Playlist) {
        // TODO: Implement playlist menu (edit, delete, refresh)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
