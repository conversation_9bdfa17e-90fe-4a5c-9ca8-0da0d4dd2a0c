package com.badboyz.iptvplayer.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.badboyz.iptvplayer.IPTVApplication
import com.badboyz.iptvplayer.data.database.dao.ChannelDao
import com.badboyz.iptvplayer.data.database.dao.EPGDao
import com.badboyz.iptvplayer.data.database.dao.PlaylistDao
import com.badboyz.iptvplayer.data.model.Channel
import com.badboyz.iptvplayer.data.model.EPGProgram
import com.badboyz.iptvplayer.data.model.Playlist

@Database(
    entities = [
        Channel::class,
        Playlist::class,
        EPGProgram::class
    ],
    version = 1,
    exportSchema = false
)
abstract class IPTVDatabase : RoomDatabase() {
    
    abstract fun channelDao(): ChannelDao
    abstract fun playlistDao(): PlaylistDao
    abstract fun epgDao(): EPGDao
    
    companion object {
        @Volatile
        private var INSTANCE: IPTVDatabase? = null
        
        fun getDatabase(): IPTVDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    IPTVApplication.instance,
                    IPTVDatabase::class.java,
                    "iptv_database"
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        // Migration example for future versions
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add migration logic here when needed
            }
        }
    }
}
