<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/card_margin"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    style="@style/CardView.Playlist">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/card_padding">

        <!-- Playlist Icon -->
        <ImageView
            android:id="@+id/iv_playlist_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="@dimen/spacing_medium"
            android:background="@drawable/channel_logo_background"
            android:contentDescription="@string/cd_playlist_icon"
            android:padding="12dp"
            android:src="@drawable/ic_playlist"
            android:tint="@color/primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Playlist Name -->
        <TextView
            android:id="@+id/tv_playlist_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/spacing_small"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Body"
            android:textSize="@dimen/text_size_medium"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/btn_playlist_menu"
            app:layout_constraintStart_toEndOf="@id/iv_playlist_icon"
            app:layout_constraintTop_toTopOf="@id/iv_playlist_icon"
            tools:text="My IPTV Playlist" />

        <!-- Channel Count -->
        <TextView
            android:id="@+id/tv_channel_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_tiny"
            android:layout_marginEnd="@dimen/spacing_small"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Caption"
            app:layout_constraintEnd_toStartOf="@id/btn_playlist_menu"
            app:layout_constraintStart_toEndOf="@id/iv_playlist_icon"
            app:layout_constraintTop_toBottomOf="@id/tv_playlist_name"
            tools:text="245 channels" />

        <!-- Last Updated -->
        <TextView
            android:id="@+id/tv_last_updated"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_tiny"
            android:layout_marginEnd="@dimen/spacing_small"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Caption"
            android:textColor="@color/text_hint"
            app:layout_constraintEnd_toStartOf="@id/btn_playlist_menu"
            app:layout_constraintStart_toEndOf="@id/iv_playlist_icon"
            app:layout_constraintTop_toBottomOf="@id/tv_channel_count"
            tools:text="Updated 2 hours ago" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_playlist_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/cd_menu_button"
            android:src="@drawable/ic_more_vert"
            android:tint="@color/text_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Status Indicator -->
        <View
            android:id="@+id/status_indicator"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:background="@drawable/status_indicator_active"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="@id/iv_playlist_icon"
            app:layout_constraintTop_toTopOf="@id/iv_playlist_icon" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
