<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Playlist Name Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_playlist_name"
        style="@style/TextInputLayout.Outlined"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="@string/playlist_name"
        app:startIconDrawable="@drawable/ic_playlist">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_playlist_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:textColor="@color/text_primary" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Playlist URL Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_playlist_url"
        style="@style/TextInputLayout.Outlined"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/playlist_url"
        app:startIconDrawable="@drawable/ic_link">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_playlist_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textUri"
            android:maxLines="3"
            android:textColor="@color/text_primary" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Helper Text -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/playlist_url_hint"
        android:textAppearance="@style/TextAppearance.Caption"
        android:textColor="@color/text_hint" />

</LinearLayout>
