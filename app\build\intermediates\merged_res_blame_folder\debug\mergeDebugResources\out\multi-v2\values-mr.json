{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c711e708460550b0e6d2719a43fe6f2e\\transformed\\material-1.10.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1052,1143,1208,1267,1355,1417,1479,1539,1606,1669,1723,1837,1894,1955,2009,2079,2198,2279,2364,2499,2576,2653,2794,2880,2964,3020,3072,3138,3208,3286,3373,3455,3525,3601,3672,3741,3855,3951,4025,4123,4219,4293,4363,4465,4520,4608,4675,4762,4855,4918,4982,5045,5111,5211,5320,5414,5521,5581,5637", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,140,85,83,55,51,65,69,77,86,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77", "endOffsets": "254,339,426,509,602,686,786,902,984,1047,1138,1203,1262,1350,1412,1474,1534,1601,1664,1718,1832,1889,1950,2004,2074,2193,2274,2359,2494,2571,2648,2789,2875,2959,3015,3067,3133,3203,3281,3368,3450,3520,3596,3667,3736,3850,3946,4020,4118,4214,4288,4358,4460,4515,4603,4670,4757,4850,4913,4977,5040,5106,5206,5315,5409,5516,5576,5632,5710"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,4083,4168,4255,4338,4431,5247,5347,5463,9685,13542,13633,13869,13928,14016,14078,14140,14200,14267,14330,14384,14498,14555,14616,14670,14740,14859,14940,15025,15160,15237,15314,15455,15541,15625,15681,15733,15799,15869,15947,16034,16116,16186,16262,16333,16402,16516,16612,16686,16784,16880,16954,17024,17126,17181,17269,17336,17423,17516,17579,17643,17706,17772,17872,17981,18075,18182,18242,18838", "endLines": "22,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,140,85,83,55,51,65,69,77,86,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77", "endOffsets": "920,4163,4250,4333,4426,4510,5342,5458,5540,9743,13628,13693,13923,14011,14073,14135,14195,14262,14325,14379,14493,14550,14611,14665,14735,14854,14935,15020,15155,15232,15309,15450,15536,15620,15676,15728,15794,15864,15942,16029,16111,16181,16257,16328,16397,16511,16607,16681,16779,16875,16949,17019,17121,17176,17264,17331,17418,17511,17574,17638,17701,17767,17867,17976,18070,18177,18237,18293,18911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7eb203db16c40ca18c3e77706606711f\\transformed\\core-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "59,60,61,62,63,64,65,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4515,4615,4719,4820,4923,5025,5130,19399", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "4610,4714,4815,4918,5020,5125,5242,19495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bf78663ddf7e5a17052df8a5e558236f\\transformed\\material3-1.1.2\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,402,523,604,700,812,944,1063,1205,1286,1388,1475,1569,1676,1802,1909,2042,2171,2295,2470,2591,2703,2819,2939,3027,3118,3234,3359,3455,3554,3659,3791,3929,4040,4133,4205,4287,4368,4454,4550,4626,4705,4800,4895,4988,5083,5166,5266,5362,5461,5575,5651,5747", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "171,290,397,518,599,695,807,939,1058,1200,1281,1383,1470,1564,1671,1797,1904,2037,2166,2290,2465,2586,2698,2814,2934,3022,3113,3229,3354,3450,3549,3654,3786,3924,4035,4128,4200,4282,4363,4449,4545,4621,4700,4795,4890,4983,5078,5161,5261,5357,5456,5570,5646,5742,5832"}, "to": {"startLines": "50,51,52,53,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,105,159,229,232,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3615,3736,3855,3962,5721,5802,5898,6010,6142,6261,6403,6484,6586,6673,6767,6874,7000,7107,7240,7369,7493,7668,7789,7901,8017,8137,8225,8316,8432,8557,8653,8752,8857,8989,9127,9238,9526,13460,19081,19313,19500,19856,19932,20011,20106,20201,20294,20389,20472,20572,20668,20767,20881,20957,21053", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "3731,3850,3957,4078,5797,5893,6005,6137,6256,6398,6479,6581,6668,6762,6869,6995,7102,7235,7364,7488,7663,7784,7896,8012,8132,8220,8311,8427,8552,8648,8747,8852,8984,9122,9233,9326,9593,13537,19157,19394,19591,19927,20006,20101,20196,20289,20384,20467,20567,20663,20762,20876,20952,21048,21138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8c441e88d76142b996bad90a67757093\\transformed\\exoplayer-ui-2.19.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1784,1903,2014,2086,2165,2236,2306,2388,2468,2532,2595,2648,2706,2754,2815,2876,2943,3005,3071,3130,3195,3260,3325,3393,3446,3506,3580,3654", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1779,1898,2009,2081,2160,2231,2301,2383,2463,2527,2590,2643,2701,2749,2810,2871,2938,3000,3066,3125,3190,3255,3320,3388,3441,3501,3575,3649,3702"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,582,9748,9830,9910,9993,10080,10174,10242,10306,10396,10487,10552,10620,10680,10748,10861,10980,11091,11163,11242,11313,11383,11465,11545,11609,12348,12401,12459,12507,12568,12629,12696,12758,12824,12883,12948,13013,13078,13146,13199,13259,13333,13407", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "376,577,761,9825,9905,9988,10075,10169,10237,10301,10391,10482,10547,10615,10675,10743,10856,10975,11086,11158,11237,11308,11378,11460,11540,11604,11667,12396,12454,12502,12563,12624,12691,12753,12819,12878,12943,13008,13073,13141,13194,13254,13328,13402,13455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c30a6e1576391bb1585dd679e4d5216\\transformed\\navigation-ui-2.7.5\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "220,221", "startColumns": "4,4", "startOffsets": "18298,18410", "endColumns": "111,116", "endOffsets": "18405,18522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5c9ad5896dbe4b4ab7793f66459dd36f\\transformed\\exoplayer-core-2.19.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11672,11742,11807,11876,11945,12020,12084,12181,12275", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "11737,11802,11871,11940,12015,12079,12176,12270,12343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\3f6383dc1c59a74ffea8dbe469a360a8\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1036,1142,1249,1339,1440,1552,1630,1707,1798,1891,1984,2081,2181,2274,2369,2463,2554,2645,2725,2832,2933,3030,3139,3241,3355,3512,19162", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "1031,1137,1244,1334,1435,1547,1625,1702,1793,1886,1979,2076,2176,2269,2364,2458,2549,2640,2720,2827,2928,3025,3134,3236,3350,3507,3610,19237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efe0e3583596feeea587a194dc746a51\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,975,1045,1125,1210,1281,1357,1423", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,970,1040,1120,1205,1276,1352,1418,1536"}, "to": {"startLines": "69,70,103,104,106,162,163,222,223,224,225,227,228,231,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5545,5639,9331,9428,9598,13698,13784,18527,18616,18699,18768,18916,18996,19242,19596,19672,19738", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "5634,5716,9423,9521,9680,13779,13864,18611,18694,18763,18833,18991,19076,19308,19667,19733,19851"}}]}]}