<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme -->
    <style name="Theme.BadBoyzIPTVPlayer" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/background_primary</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background_primary</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <!-- Window properties -->
        <item name="android:windowBackground">@color/background_primary</item>
        <item name="android:windowAnimationStyle">@style/WindowAnimationTransition</item>
    </style>
    
    <!-- No Action Bar theme -->
    <style name="Theme.BadBoyzIPTVPlayer.NoActionBar" parent="Theme.BadBoyzIPTVPlayer">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <!-- Fullscreen theme for player -->
    <style name="Theme.BadBoyzIPTVPlayer.Fullscreen" parent="Theme.BadBoyzIPTVPlayer.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@color/player_background</item>
        <item name="android:keepScreenOn">true</item>
    </style>
    
    <!-- Splash theme -->
    <style name="Theme.BadBoyzIPTVPlayer.Splash" parent="Theme.BadBoyzIPTVPlayer.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>
    
    <!-- Card styles -->
    <style name="CardView.Channel" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="strokeColor">@color/card_stroke</item>
        <item name="strokeWidth">1dp</item>
    </style>
    
    <style name="CardView.Playlist" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">6dp</item>
        <item name="strokeColor">@color/card_stroke</item>
        <item name="strokeWidth">1dp</item>
    </style>
    
    <!-- Button styles -->
    <style name="Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/primary</item>
        <item name="android:textColor">@color/primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>
    
    <!-- Text styles -->
    <style name="TextAppearance.Title" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>
    
    <style name="TextAppearance.Subtitle" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>
    
    <style name="TextAppearance.Body" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>
    
    <style name="TextAppearance.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>
    
    <!-- Input styles -->
    <style name="TextInputLayout.Outlined" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>
    
    <!-- Window animation -->
    <style name="WindowAnimationTransition">
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_left</item>
    </style>
    
    <!-- Toolbar style -->
    <style name="Toolbar.Primary" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/background_secondary</item>
        <item name="titleTextColor">@color/text_primary</item>
        <item name="subtitleTextColor">@color/text_secondary</item>
        <item name="android:elevation">4dp</item>
    </style>
    
    <!-- Bottom Navigation style -->
    <style name="BottomNavigation.Primary" parent="Widget.Material3.BottomNavigationView">
        <item name="android:background">@color/background_secondary</item>
        <item name="itemIconTint">@color/bottom_nav_color</item>
        <item name="itemTextColor">@color/bottom_nav_color</item>
        <item name="android:elevation">8dp</item>
    </style>
</resources>
