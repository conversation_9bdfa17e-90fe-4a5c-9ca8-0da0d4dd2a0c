package com.badboyz.iptvplayer.data.repository

import androidx.lifecycle.LiveData
import com.badboyz.iptvplayer.data.database.dao.ChannelDao
import com.badboyz.iptvplayer.data.database.dao.PlaylistDao
import com.badboyz.iptvplayer.data.model.Channel
import com.badboyz.iptvplayer.data.model.Playlist
import com.badboyz.iptvplayer.data.network.PlaylistService
import com.badboyz.iptvplayer.data.parser.M3UParser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PlaylistRepository @Inject constructor(
    private val playlistDao: PlaylistDao,
    private val channelDao: ChannelDao,
    private val playlistService: PlaylistService,
    private val m3uParser: M3UParser
) {
    
    fun getAllPlaylists(): Flow<List<Playlist>> = playlistDao.getAllPlaylists()
    
    fun getAllPlaylistsLiveData(): LiveData<List<Playlist>> = playlistDao.getAllPlaylistsLiveData()
    
    fun getActivePlaylists(): Flow<List<Playlist>> = playlistDao.getActivePlaylists()
    
    suspend fun getPlaylistById(playlistId: Long): Playlist? = playlistDao.getPlaylistById(playlistId)
    
    suspend fun addPlaylist(playlist: Playlist): Result<Long> {
        return try {
            val existingPlaylist = playlistDao.getPlaylistByUrl(playlist.url)
            if (existingPlaylist != null) {
                return Result.failure(Exception("Playlist with this URL already exists"))
            }
            
            val playlistId = playlistDao.insertPlaylist(playlist)
            loadPlaylistChannels(playlistId, playlist.url)
            Result.success(playlistId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updatePlaylist(playlist: Playlist): Result<Unit> {
        return try {
            playlistDao.updatePlaylist(playlist)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun deletePlaylist(playlistId: Long): Result<Unit> {
        return try {
            channelDao.deleteChannelsByPlaylist(playlistId)
            playlistDao.deletePlaylistById(playlistId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun refreshPlaylist(playlistId: Long): Result<Unit> {
        return try {
            val playlist = playlistDao.getPlaylistById(playlistId)
                ?: return Result.failure(Exception("Playlist not found"))
            
            // Delete existing channels
            channelDao.deleteChannelsByPlaylist(playlistId)
            
            // Reload channels
            loadPlaylistChannels(playlistId, playlist.url)
            
            // Update last refresh time
            playlistDao.updateLastRefresh(playlistId)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private suspend fun loadPlaylistChannels(playlistId: Long, url: String) = withContext(Dispatchers.IO) {
        try {
            val content = if (url.startsWith("http")) {
                playlistService.downloadPlaylist(url)
            } else {
                // Handle local file
                // TODO: Implement local file reading
                throw Exception("Local file support not implemented yet")
            }
            
            if (!m3uParser.validateM3U(content)) {
                throw Exception("Invalid M3U format")
            }
            
            val channels = m3uParser.parseM3U(content, playlistId)
            channelDao.insertChannels(channels)
            
            // Update channel count
            playlistDao.updateChannelCount(playlistId, channels.size)
            
        } catch (e: Exception) {
            throw Exception("Failed to load playlist: ${e.message}")
        }
    }
    
    suspend fun importPlaylistFromUrl(name: String, url: String): Result<Long> {
        return try {
            val playlist = Playlist(
                name = name,
                url = url,
                isLocal = false,
                lastUpdated = System.currentTimeMillis()
            )
            addPlaylist(playlist)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getPlaylistChannelCount(playlistId: Long): Int {
        return channelDao.getChannelCountByPlaylist(playlistId)
    }
}
