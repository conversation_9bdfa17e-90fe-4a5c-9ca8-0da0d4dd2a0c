<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.main.MainActivity">

    <!-- Main content -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- App Bar -->
        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/background_secondary"
            android:elevation="4dp"
            android:theme="@style/ThemeOverlay.Material3.Dark">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/background_secondary"
                app:title="@string/app_name"
                app:titleTextColor="@color/text_primary"
                app:navigationIcon="@drawable/ic_menu"
                app:menu="@menu/main_menu" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Main content container -->
        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/nav_host_fragment"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:defaultNavHost="true"
            app:navGraph="@navigation/nav_graph" />

        <!-- Bottom Navigation -->
        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@color/background_secondary"
            android:elevation="8dp"
            app:itemIconTint="@color/bottom_nav_color"
            app:itemTextColor="@color/bottom_nav_color"
            app:menu="@menu/bottom_nav_menu" />

        <!-- Floating Action Button -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_add_playlist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:contentDescription="@string/add_playlist"
            android:src="@drawable/ic_add"
            app:layout_anchor="@id/bottom_navigation"
            app:layout_anchorGravity="top|end"
            app:backgroundTint="@color/primary"
            app:tint="@color/white" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/background_secondary"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/drawer_menu"
        app:itemIconTint="@color/text_secondary"
        app:itemTextColor="@color/text_primary" />

</androidx.drawerlayout.widget.DrawerLayout>
