package com.badboyz.iptvplayer.data.repository

import androidx.lifecycle.LiveData
import com.badboyz.iptvplayer.data.database.dao.EPGDao
import com.badboyz.iptvplayer.data.model.EPGProgram
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class EPGRepository @Inject constructor(
    private val epgDao: EPGDao
) {
    
    fun getProgramsByChannel(channelId: String): Flow<List<EPGProgram>> = 
        epgDao.getProgramsByChannel(channelId)
    
    fun getProgramsByChannelLiveData(channelId: String): LiveData<List<EPGProgram>> = 
        epgDao.getProgramsByChannelLiveData(channelId)
    
    suspend fun getCurrentProgram(channelId: String): EPGProgram? = 
        epgDao.getCurrentProgram(channelId)
    
    suspend fun getNextProgram(channelId: String): EPGProgram? = 
        epgDao.getNextProgram(channelId)
    
    fun getProgramsByChannelAndTimeRange(
        channelId: String, 
        startTime: Long, 
        endTime: Long
    ): Flow<List<EPGProgram>> = 
        epgDao.getProgramsByChannelAndTimeRange(channelId, startTime, endTime)
    
    fun searchPrograms(query: String): Flow<List<EPGProgram>> = 
        epgDao.searchPrograms(query)
    
    suspend fun insertProgram(program: EPGProgram): Result<Long> {
        return try {
            val id = epgDao.insertProgram(program)
            Result.success(id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun insertPrograms(programs: List<EPGProgram>): Result<Unit> {
        return try {
            epgDao.insertPrograms(programs)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updateProgram(program: EPGProgram): Result<Unit> {
        return try {
            epgDao.updateProgram(program)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun deleteProgram(program: EPGProgram): Result<Unit> {
        return try {
            epgDao.deleteProgram(program)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun deleteProgramsByChannel(channelId: String): Result<Unit> {
        return try {
            epgDao.deleteProgramsByChannel(channelId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun deleteOldPrograms(): Result<Unit> {
        return try {
            epgDao.deleteOldPrograms()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getProgramCountByChannel(channelId: String): Int = 
        epgDao.getProgramCountByChannel(channelId)
}
