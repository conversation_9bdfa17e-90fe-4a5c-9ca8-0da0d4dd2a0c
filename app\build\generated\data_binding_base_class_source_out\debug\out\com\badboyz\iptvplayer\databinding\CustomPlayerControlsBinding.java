// Generated by view binder compiler. Do not edit!
package com.badboyz.iptvplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.media3.ui.DefaultTimeBar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.badboyz.iptvplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CustomPlayerControlsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView exoDuration;

  @NonNull
  public final ImageButton exoFfwd;

  @NonNull
  public final ImageButton exoNext;

  @NonNull
  public final ImageButton exoPause;

  @NonNull
  public final ImageButton exoPlay;

  @NonNull
  public final TextView exoPosition;

  @NonNull
  public final ImageButton exoPrev;

  @NonNull
  public final DefaultTimeBar exoProgress;

  @NonNull
  public final ImageButton exoRew;

  private CustomPlayerControlsBinding(@NonNull LinearLayout rootView, @NonNull TextView exoDuration,
      @NonNull ImageButton exoFfwd, @NonNull ImageButton exoNext, @NonNull ImageButton exoPause,
      @NonNull ImageButton exoPlay, @NonNull TextView exoPosition, @NonNull ImageButton exoPrev,
      @NonNull DefaultTimeBar exoProgress, @NonNull ImageButton exoRew) {
    this.rootView = rootView;
    this.exoDuration = exoDuration;
    this.exoFfwd = exoFfwd;
    this.exoNext = exoNext;
    this.exoPause = exoPause;
    this.exoPlay = exoPlay;
    this.exoPosition = exoPosition;
    this.exoPrev = exoPrev;
    this.exoProgress = exoProgress;
    this.exoRew = exoRew;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static CustomPlayerControlsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CustomPlayerControlsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.custom_player_controls, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CustomPlayerControlsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = androidx.media3.ui.R.id.exo_duration;
      TextView exoDuration = ViewBindings.findChildViewById(rootView, id);
      if (exoDuration == null) {
        break missingId;
      }

      id = androidx.media3.ui.R.id.exo_ffwd;
      ImageButton exoFfwd = ViewBindings.findChildViewById(rootView, id);
      if (exoFfwd == null) {
        break missingId;
      }

      id = androidx.media3.ui.R.id.exo_next;
      ImageButton exoNext = ViewBindings.findChildViewById(rootView, id);
      if (exoNext == null) {
        break missingId;
      }

      id = androidx.media3.ui.R.id.exo_pause;
      ImageButton exoPause = ViewBindings.findChildViewById(rootView, id);
      if (exoPause == null) {
        break missingId;
      }

      id = androidx.media3.ui.R.id.exo_play;
      ImageButton exoPlay = ViewBindings.findChildViewById(rootView, id);
      if (exoPlay == null) {
        break missingId;
      }

      id = androidx.media3.ui.R.id.exo_position;
      TextView exoPosition = ViewBindings.findChildViewById(rootView, id);
      if (exoPosition == null) {
        break missingId;
      }

      id = androidx.media3.ui.R.id.exo_prev;
      ImageButton exoPrev = ViewBindings.findChildViewById(rootView, id);
      if (exoPrev == null) {
        break missingId;
      }

      id = androidx.media3.ui.R.id.exo_progress;
      DefaultTimeBar exoProgress = ViewBindings.findChildViewById(rootView, id);
      if (exoProgress == null) {
        break missingId;
      }

      id = androidx.media3.ui.R.id.exo_rew;
      ImageButton exoRew = ViewBindings.findChildViewById(rootView, id);
      if (exoRew == null) {
        break missingId;
      }

      return new CustomPlayerControlsBinding((LinearLayout) rootView, exoDuration, exoFfwd, exoNext,
          exoPause, exoPlay, exoPosition, exoPrev, exoProgress, exoRew);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
