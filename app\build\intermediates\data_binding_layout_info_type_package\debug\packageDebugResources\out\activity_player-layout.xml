<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_player" modulePackage="com.badboyz.iptvplayer" filePath="app\src\main\res\layout\activity_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_player_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="152" endOffset="51"/></Target><Target id="@+id/player_view" view="com.google.android.exoplayer2.ui.PlayerView"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="35"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="21" startOffset="4" endLine="31" endOffset="36"/></Target><Target id="@+id/tv_error" view="TextView"><Expressions/><location startLine="34" startOffset="4" endLine="48" endOffset="36"/></Target><Target id="@+id/controls_overlay" view="LinearLayout"><Expressions/><location startLine="51" startOffset="4" endLine="150" endOffset="18"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="69" startOffset="12" endLine="76" endOffset="45"/></Target><Target id="@+id/tv_channel_name" view="TextView"><Expressions/><location startLine="86" startOffset="16" endLine="93" endOffset="47"/></Target><Target id="@+id/tv_channel_group" view="TextView"><Expressions/><location startLine="95" startOffset="16" endLine="103" endOffset="48"/></Target><Target id="@+id/btn_fullscreen" view="ImageButton"><Expressions/><location startLine="108" startOffset="12" endLine="115" endOffset="45"/></Target><Target id="@+id/tv_now_playing" view="TextView"><Expressions/><location startLine="127" startOffset="12" endLine="135" endOffset="44"/></Target><Target id="@+id/tv_next_program" view="TextView"><Expressions/><location startLine="137" startOffset="12" endLine="146" endOffset="44"/></Target></Targets></Layout>