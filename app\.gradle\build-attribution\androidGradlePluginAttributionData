{"tasksSharingOutput": [], "garbageCollectionData": [{"gcName": "G1 Young Generation", "duration": 431}], "buildSrcPlugins": [], "javaInfo": {"javaVersion": "17.0.11", "javaVendor": "JetBrains s.r.o.", "javaHome": "C:\\Program Files\\Android\\Android Studio\\jbr", "vmArguments": ["-XX:MaxMetaspaceSize=512m", "-XX:+HeapDumpOnOutOfMemoryError", "--add-opens=java.base/java.util=ALL-UNNAMED", "--add-opens=java.base/java.lang=ALL-UNNAMED", "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED", "--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED", "--add-opens=java.base/java.nio.charset=ALL-UNNAMED", "--add-opens=java.base/java.net=ALL-UNNAMED", "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED", "-Xmx4096m", "-Dfile.encoding=UTF-8", "-Duser.country=US", "-Duser.language=en", "-Duser.variant", "-javaagent:C:\\Users\\<USER>\\.gradle\\wrapper\\dists\\gradle-8.7-bin\\bhs2wmbdwecv87pi65oeuq5iu\\gradle-8.7\\lib\\agents\\gradle-instrumentation-agent-8.7.jar"]}, "buildscriptDependencies": ["com.google.dagger:hilt-android-gradle-plugin:2.48", "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20", "org.jetbrains.kotlin:kotlin-stdlib:1.9.20", "org.jetbrains:annotations:23.0.0", "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20", "org.ow2.asm:asm:9.6", "com.squareup:javapoet:1.13.0", "com.android.application:com.android.application.gradle.plugin:8.5.1", "com.android.tools.build:gradle:8.5.1", "com.android.tools.build:gradle-settings-api:8.5.1", "com.android.tools:sdk-common:31.5.1", "com.android.tools.analytics-library:shared:31.5.1", "com.android.tools.analytics-library:protos:31.5.1", "com.google.protobuf:protobuf-java:3.22.3", "com.android.tools:annotations:31.5.1", "com.android.tools:common:31.5.1", "com.google.guava:guava:32.0.1-jre", "com.google.guava:failureaccess:1.0.1", "com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava", "com.google.code.findbugs:jsr305:3.0.2", "org.checkerframework:checker-qual:3.33.0", "com.google.errorprone:error_prone_annotations:2.18.0", "com.google.j2objc:j2objc-annotations:2.8", "net.java.dev.jna:jna-platform:5.6.0", "net.java.dev.jna:jna:5.6.0", "com.google.code.gson:gson:2.10.1", "com.android.tools.build:aapt2-proto:8.5.1-11315950", "com.android.tools.ddms:ddmlib:31.5.1", "net.sf.kxml:kxml2:2.3.0", "com.android.tools.layoutlib:layoutlib-api:31.5.1", "com.android.tools:sdklib:31.5.1", "com.android.tools:repository:31.5.1", "com.google.jimfs:jimfs:1.1", "com.sun.activation:javax.activation:1.2.0", "org.apache.commons:commons-compress:1.21", "org.glassfish.jaxb:jaxb-runtime:2.3.2", "jakarta.xml.bind:jakarta.xml.bind-api:2.3.2", "jakarta.activation:jakarta.activation-api:1.2.1", "org.glassfish.jaxb:txw2:2.3.2", "com.sun.istack:istack-commons-runtime:3.0.8", "org.jvnet.staxex:stax-ex:1.8.1", "com.sun.xml.fastinfoset:FastInfoset:1.2.16", "com.android.tools:dvlib:31.5.1", "org.apache.httpcomponents:httpcore:4.4.16", "org.apache.httpcomponents:httpmime:4.5.6", "org.apache.httpcomponents:httpclient:4.5.14", "commons-logging:commons-logging:1.2", "commons-codec:commons-codec:1.11", "javax.inject:javax.inject:1", "org.bouncycastle:bcpkix-jdk18on:1.77", "org.bouncycastle:bcprov-jdk18on:1.77", "org.bouncycastle:bcutil-jdk18on:1.77", "org.jetbrains.intellij.deps:trove4j:1.0.20200330", "org.jetbrains.kotlin:kotlin-reflect:1.9.20", "com.android.tools.build:aaptcompiler:8.5.1", "com.android.tools.analytics-library:crash:31.5.1", "com.android.tools.lint:lint-model:31.5.1", "com.android.tools.build:builder-model:8.5.1", "com.android.tools.lint:lint-typedef-remover:31.5.1", "androidx.databinding:databinding-compiler-common:8.5.1", "androidx.databinding:databinding-common:8.5.1", "com.android.databinding:baseLibrary:8.5.1", "com.android.tools.build.jetifier:jetifier-core:1.0.0-beta10", "com.googlecode.juniversalchardet:juniversalchardet:1.0.3", "commons-io:commons-io:2.13.0", "com.android.tools.build:builder-test-api:8.5.1", "com.android.tools.utp:android-device-provider-ddmlib-proto:31.5.1", "com.android.tools.utp:android-device-provider-gradle-proto:31.5.1", "com.android.tools.utp:android-test-plugin-host-additional-test-output-proto:31.5.1", "com.android.tools.utp:android-test-plugin-host-coverage-proto:31.5.1", "com.android.tools.utp:android-test-plugin-host-emulator-control-proto:31.5.1", "com.android.tools.utp:android-test-plugin-host-logcat-proto:31.5.1", "com.android.tools.utp:android-test-plugin-host-apk-installer-proto:31.5.1", "com.android.tools.utp:android-test-plugin-host-retention-proto:31.5.1", "com.android.tools.utp:android-test-plugin-result-listener-gradle-proto:31.5.1", "io.grpc:grpc-core:1.57.0", "io.grpc:grpc-api:1.57.0", "com.google.android:annotations:*******", "org.codehaus.mojo:animal-sniffer-annotations:1.23", "io.perfmark:perfmark-api:0.26.0", "io.grpc:grpc-context:1.57.0", "io.grpc:grpc-netty:1.57.0", "io.netty:netty-codec-http2:4.1.93.Final", "io.netty:netty-common:4.1.93.Final", "io.netty:netty-buffer:4.1.93.Final", "io.netty:netty-transport:4.1.93.Final", "io.netty:netty-resolver:4.1.93.Final", "io.netty:netty-codec:4.1.93.Final", "io.netty:netty-handler:4.1.93.Final", "io.netty:netty-transport-native-unix-common:4.1.93.Final", "io.netty:netty-codec-http:4.1.93.Final", "io.netty:netty-handler-proxy:4.1.93.Final", "io.netty:netty-codec-socks:4.1.93.Final", "io.grpc:grpc-protobuf:1.57.0", "com.google.api.grpc:proto-google-common-protos:2.17.0", "io.grpc:grpc-protobuf-lite:1.57.0", "io.grpc:grpc-stub:1.57.0", "javax.annotation:javax.annotation-api:1.3.2", "com.android.tools.build:transform-api:2.0.0-deprecated-use-gradle-api", "org.ow2.asm:asm-analysis:9.6", "org.ow2.asm:asm-tree:9.6", "org.ow2.asm:asm-commons:9.6", "org.ow2.asm:asm-util:9.6", "net.sf.jopt-simple:jopt-simple:4.9", "com.android.tools.build:bundletool:1.16.0", "com.google.auto.value:auto-value-annotations:1.6.2", "com.google.protobuf:protobuf-java-util:3.22.3", "com.google.dagger:dagger:2.28.3", "org.bitbucket.b_c:jose4j:0.9.5", "org.slf4j:slf4j-api:1.7.30", "com.android.tools.build.jetifier:jetifier-processor:1.0.0-beta10", "org.jdom:jdom2:2.0.6", "com.google.crypto.tink:tink:1.7.0", "com.google.testing.platform:core-proto:0.0.9-alpha02", "com.google.flatbuffers:flatbuffers-java:1.12.0", "org.tensorflow:tensorflow-lite-metadata:0.1.0-rc2", "com.android.tools.build:builder:8.5.1", "com.android:signflinger:8.5.1", "com.android.tools.build:apksig:8.5.1", "com.android:zipflinger:8.5.1", "com.android.tools.analytics-library:tracker:31.5.1", "com.android.tools.build:manifest-merger:31.5.1", "com.android.tools.build:apkzlib:8.5.1", "com.squareup:javawriter:2.5.0", "com.android.tools.build:gradle-api:8.5.1", "com.android.library:com.android.library.gradle.plugin:8.5.1", "org.jetbrains.kotlin.android:org.jetbrains.kotlin.android.gradle.plugin:1.9.10", "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.10", "org.jetbrains.kotlin:kotlin-gradle-plugin-api:1.9.10", "org.jetbrains.kotlin:kotlin-gradle-plugins-bom:1.9.10", "org.jetbrains.kotlin:kotlin-gradle-plugin-model:1.9.10", "org.jetbrains.kotlin:kotlin-tooling-core:1.9.10", "org.jetbrains.kotlin:kotlin-gradle-plugin-annotations:1.9.10", "org.jetbrains.kotlin:kotlin-native-utils:1.9.10", "org.jetbrains.kotlin:kotlin-util-io:1.9.10", "org.jetbrains.kotlin:kotlin-util-klib:1.9.10", "org.jetbrains.kotlin:kotlin-project-model:1.9.10", "org.jetbrains.kotlin:kotlin-gradle-plugin-idea:1.9.10", "org.jetbrains.kotlin:kotlin-gradle-plugin-idea-proto:1.9.10", "org.jetbrains.kotlin:kotlin-klib-commonizer-api:1.9.10", "org.jetbrains.kotlin:kotlin-build-tools-api:1.9.10", "org.jetbrains.kotlin:kotlin-compiler-embeddable:1.9.10", "org.jetbrains.kotlin:kotlin-daemon-embeddable:1.9.10", "org.jetbrains.kotlin:kotlin-android-extensions:1.9.10", "org.jetbrains.kotlin:kotlin-compiler-runner:1.9.10", "org.jetbrains.kotlin:kotlin-daemon-client:1.9.10", "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.0", "org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable:1.9.10", "org.jetbrains.kotlin:kotlin-scripting-compiler-impl-embeddable:1.9.10", "org.jetbrains.kotlin:kotlin-scripting-common:1.9.10", "org.jetbrains.kotlin:kotlin-scripting-jvm:1.9.10"], "buildInfo": {"agpVersion": "8.5.1", "gradleVersion": "8.7", "configurationCacheIsOn": true}, "taskNameToTaskInfoMap": [{"taskName": "preBuild", "className": "org.gradle.api.DefaultTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "preDebugBuild", "className": "com.android.build.gradle.internal.tasks.AndroidVariantTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "dataBindingMergeDependencyArtifactsDebug", "className": "com.android.build.gradle.internal.tasks.databinding.DataBindingMergeDependencyArtifactsTask", "primaryTaskCategory": "DATA_BINDING", "secondaryTaskCategories": ["MERGING"]}, {"taskName": "generateDebugResValues", "className": "com.android.build.gradle.tasks.GenerateResValues", "primaryTaskCategory": "ANDROID_RESOURCES", "secondaryTaskCategories": ["SOURCE_GENERATION"]}, {"taskName": "generateDebugResources", "className": "com.android.build.gradle.internal.tasks.ValidateResourcesTask", "primaryTaskCategory": "VERIFICATION", "secondaryTaskCategories": []}, {"taskName": "mergeDebugResources", "className": "com.android.build.gradle.tasks.MergeResources", "primaryTaskCategory": "ANDROID_RESOURCES", "secondaryTaskCategories": ["MERGING"]}, {"taskName": "packageDebugResources", "className": "com.android.build.gradle.tasks.MergeResources", "primaryTaskCategory": "ANDROID_RESOURCES", "secondaryTaskCategories": ["MERGING"]}, {"taskName": "parseDebugLocalResources", "className": "com.android.build.gradle.internal.res.ParseLibraryResourcesTask", "primaryTaskCategory": "ANDROID_RESOURCES", "secondaryTaskCategories": []}, {"taskName": "dataBindingGenBaseClassesDebug", "className": "com.android.build.gradle.internal.tasks.databinding.DataBindingGenBaseClassesTask", "primaryTaskCategory": "DATA_BINDING", "secondaryTaskCategories": ["SOURCE_GENERATION"]}, {"taskName": "dataBindingTriggerDebug", "className": "com.android.build.gradle.internal.tasks.databinding.DataBindingTriggerTask", "primaryTaskCategory": "DATA_BINDING", "secondaryTaskCategories": []}, {"taskName": "checkDebugAarMetadata", "className": "com.android.build.gradle.internal.tasks.CheckAarMetadataTask", "primaryTaskCategory": "VERIFICATION", "secondaryTaskCategories": []}, {"taskName": "mapDebugSourceSetPaths", "className": "com.android.build.gradle.tasks.MapSourceSetPathsTask", "primaryTaskCategory": "ANDROID_RESOURCES", "secondaryTaskCategories": []}, {"taskName": "createDebugCompatibleScreenManifests", "className": "com.android.build.gradle.tasks.CompatibleScreensManifest", "primaryTaskCategory": "MANIFEST", "secondaryTaskCategories": ["SOURCE_GENERATION"]}, {"taskName": "extractDeepLinksDebug", "className": "com.android.build.gradle.tasks.ExtractDeepLinksTask", "primaryTaskCategory": "ANDROID_RESOURCES", "secondaryTaskCategories": []}, {"taskName": "processDebugMainManifest", "className": "com.android.build.gradle.tasks.ProcessApplicationManifest", "primaryTaskCategory": "MANIFEST", "secondaryTaskCategories": []}, {"taskName": "processDebugManifest", "className": "com.android.build.gradle.tasks.ProcessMultiApkApplicationManifest", "primaryTaskCategory": "MANIFEST", "secondaryTaskCategories": []}, {"taskName": "processDebugManifestForPackage", "className": "com.android.build.gradle.tasks.ProcessPackagedManifestTask", "primaryTaskCategory": "MANIFEST", "secondaryTaskCategories": []}, {"taskName": "processDebugResources", "className": "com.android.build.gradle.internal.res.LinkApplicationAndroidResourcesTask", "primaryTaskCategory": "ANDROID_RESOURCES", "secondaryTaskCategories": ["LINKING"]}, {"taskName": "kaptGenerateStubsDebugKotlin", "className": "org.jetbrains.kotlin.gradle.internal.KaptGenerateStubsTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "kaptDebugK<PERSON>lin", "className": "org.jetbrains.kotlin.gradle.internal.KaptWithoutKotlincTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "compileDebug<PERSON><PERSON>lin", "className": "org.jetbrains.kotlin.gradle.tasks.KotlinCompile", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "javaPreCompileDebug", "className": "com.android.build.gradle.tasks.JavaPreCompileTask", "primaryTaskCategory": "JAVA", "secondaryTaskCategories": ["COMPILATION"]}, {"taskName": "compileDebugJavaWithJavac", "className": "org.gradle.api.tasks.compile.JavaCompile", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "hiltAggregateDepsDebug", "className": "dagger.hilt.android.plugin.task.AggregateDepsTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "hiltJavaCompileDebug", "className": "org.gradle.api.tasks.compile.JavaCompile", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "transformDebugClassesWithAsm", "className": "com.android.build.gradle.tasks.TransformClassesWithAsmTask", "primaryTaskCategory": "COMPILED_CLASSES", "secondaryTaskCategories": ["SOURCE_PROCESSING"]}, {"taskName": "bundleDebugClassesToRuntimeJar", "className": "com.android.build.gradle.internal.feature.BundleAllClasses", "primaryTaskCategory": "COMPILED_CLASSES", "secondaryTaskCategories": ["ZIPPING"]}, {"taskName": "preDebugUnitTestBuild", "className": "com.android.build.gradle.internal.tasks.AndroidVariantTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "processDebugJavaRes", "className": "com.android.build.gradle.internal.tasks.ProcessJavaResTask", "primaryTaskCategory": "JAVA_RESOURCES", "secondaryTaskCategories": []}, {"taskName": "bundleDebugClassesToCompileJar", "className": "com.android.build.gradle.internal.feature.BundleAllClasses", "primaryTaskCategory": "COMPILED_CLASSES", "secondaryTaskCategories": ["ZIPPING"]}, {"taskName": "kaptGenerateStubsDebugUnitTestKotlin", "className": "org.jetbrains.kotlin.gradle.internal.KaptGenerateStubsTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "kaptDebugUnitTestKotlin", "className": "org.jetbrains.kotlin.gradle.internal.KaptWithoutKotlincTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "compileDebugUnitTestKotlin", "className": "org.jetbrains.kotlin.gradle.tasks.KotlinCompile", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "javaPreCompileDebugUnitTest", "className": "com.android.build.gradle.tasks.JavaPreCompileTask", "primaryTaskCategory": "JAVA", "secondaryTaskCategories": ["COMPILATION"]}, {"taskName": "compileDebugUnitTestJavaWithJavac", "className": "org.gradle.api.tasks.compile.JavaCompile", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "hiltAggregateDepsDebugUnitTest", "className": "dagger.hilt.android.plugin.task.AggregateDepsTask", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "hiltJavaCompileDebugUnitTest", "className": "org.gradle.api.tasks.compile.JavaCompile", "primaryTaskCategory": "UNCATEGORIZED", "secondaryTaskCategories": []}, {"taskName": "processDebugUnitTestJavaRes", "className": "com.android.build.gradle.internal.tasks.ProcessJavaResTask", "primaryTaskCategory": "JAVA_RESOURCES", "secondaryTaskCategories": []}, {"taskName": "transformDebugUnitTestClassesWithAsm", "className": "com.android.build.gradle.tasks.TransformClassesWithAsmTask", "primaryTaskCategory": "COMPILED_CLASSES", "secondaryTaskCategories": ["SOURCE_PROCESSING"]}, {"taskName": "testDebugUnitTest", "className": "com.android.build.gradle.tasks.factory.AndroidUnitTest", "primaryTaskCategory": "TEST", "secondaryTaskCategories": []}], "taskCategoryIssues": []}