<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="custom_player_controls" modulePackage="com.badboyz.iptvplayer" filePath="app\src\main\res\layout\custom_player_controls.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/custom_player_controls_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="141" endOffset="14"/></Target><Target id="@id/exo_position" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="34" endOffset="48"/></Target><Target id="@id/exo_progress" view="androidx.media3.ui.DefaultTimeBar"><Expressions/><location startLine="37" startOffset="8" endLine="47" endOffset="44"/></Target><Target id="@id/exo_duration" view="TextView"><Expressions/><location startLine="50" startOffset="8" endLine="57" endOffset="48"/></Target><Target id="@id/exo_prev" view="ImageButton"><Expressions/><location startLine="70" startOffset="8" endLine="79" endOffset="84"/></Target><Target id="@id/exo_rew" view="ImageButton"><Expressions/><location startLine="82" startOffset="8" endLine="91" endOffset="82"/></Target><Target id="@id/exo_play" view="ImageButton"><Expressions/><location startLine="94" startOffset="8" endLine="103" endOffset="80"/></Target><Target id="@id/exo_pause" view="ImageButton"><Expressions/><location startLine="105" startOffset="8" endLine="114" endOffset="81"/></Target><Target id="@id/exo_ffwd" view="ImageButton"><Expressions/><location startLine="117" startOffset="8" endLine="126" endOffset="87"/></Target><Target id="@id/exo_next" view="ImageButton"><Expressions/><location startLine="129" startOffset="8" endLine="137" endOffset="80"/></Target></Targets></Layout>