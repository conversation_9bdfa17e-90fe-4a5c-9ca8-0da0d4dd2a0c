<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="160dp"
    android:layout_height="120dp"
    android:layout_marginEnd="@dimen/spacing_small"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    style="@style/CardView.Channel">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/spacing_small">

        <!-- Channel Logo -->
        <ImageView
            android:id="@+id/iv_channel_logo"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/channel_logo_background"
            android:contentDescription="@string/cd_channel_logo"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_tv" />

        <!-- Favorite Button -->
        <ImageButton
            android:id="@+id/btn_favorite"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/cd_favorite_button"
            android:src="@drawable/ic_favorite_border"
            android:tint="@color/text_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Channel Name -->
        <TextView
            android:id="@+id/tv_channel_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_small"
            android:ellipsize="end"
            android:maxLines="2"
            android:textAppearance="@style/TextAppearance.Body"
            android:textSize="@dimen/text_size_small"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_channel_logo"
            tools:text="Channel Name" />

        <!-- Channel Group -->
        <TextView
            android:id="@+id/tv_channel_group"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_tiny"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Caption"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_channel_name"
            tools:text="Entertainment" />

        <!-- Live Indicator -->
        <TextView
            android:id="@+id/tv_live_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/live_indicator_background"
            android:paddingHorizontal="4dp"
            android:paddingVertical="2dp"
            android:text="@string/live"
            android:textColor="@color/white"
            android:textSize="8sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/iv_channel_logo"
            app:layout_constraintTop_toTopOf="@id/iv_channel_logo"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
