package com.badboyz.iptvplayer.data.network

import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.http.GET
import retrofit2.http.Url
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

interface PlaylistApi {
    @GET
    suspend fun downloadPlaylist(@Url url: String): Response<ResponseBody>
}

@Singleton
class PlaylistService @Inject constructor(
    private val playlistApi: PlaylistApi,
    private val okHttpClient: OkHttpClient
) {
    
    suspend fun downloadPlaylist(url: String): String {
        return try {
            val response = playlistApi.downloadPlaylist(url)
            if (response.isSuccessful) {
                response.body()?.string() ?: throw IOException("Empty response body")
            } else {
                throw IOException("HTTP ${response.code()}: ${response.message()}")
            }
        } catch (e: Exception) {
            throw IOException("Failed to download playlist: ${e.message}")
        }
    }
    
    suspend fun downloadPlaylistWithCustomHeaders(
        url: String, 
        userAgent: String? = null,
        referer: String? = null,
        headers: Map<String, String>? = null
    ): String {
        return try {
            val requestBuilder = Request.Builder().url(url)
            
            userAgent?.let { requestBuilder.addHeader("User-Agent", it) }
            referer?.let { requestBuilder.addHeader("Referer", it) }
            headers?.forEach { (key, value) -> requestBuilder.addHeader(key, value) }
            
            val request = requestBuilder.build()
            val response = okHttpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                response.body?.string() ?: throw IOException("Empty response body")
            } else {
                throw IOException("HTTP ${response.code}: ${response.message}")
            }
        } catch (e: Exception) {
            throw IOException("Failed to download playlist: ${e.message}")
        }
    }
    
    suspend fun validatePlaylistUrl(url: String): Boolean {
        return try {
            val request = Request.Builder()
                .url(url)
                .head() // Use HEAD request to check if URL is accessible
                .build()
            
            val response = okHttpClient.newCall(request).execute()
            response.isSuccessful
        } catch (e: Exception) {
            false
        }
    }
}
