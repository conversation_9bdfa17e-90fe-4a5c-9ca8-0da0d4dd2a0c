<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Welcome Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@drawable/gradient_background"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/welcome_title"
                    android:textAppearance="@style/TextAppearance.Title"
                    android:textColor="@color/white"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/welcome_subtitle"
                    android:textAppearance="@style/TextAppearance.Subtitle"
                    android:textColor="@color/white"
                    android:alpha="0.9" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Quick Actions -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:orientation="horizontal"
            android:weightSum="2">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_add_playlist"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardBackgroundColor="@color/card_background"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:strokeColor="@color/card_stroke"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginBottom="8dp"
                        android:src="@drawable/ic_add"
                        android:tint="@color/primary"
                        android:contentDescription="@string/add_playlist" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/add_playlist"
                        android:textAppearance="@style/TextAppearance.Body"
                        android:textAlignment="center" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_favorites"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardBackgroundColor="@color/card_background"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:strokeColor="@color/card_stroke"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginBottom="8dp"
                        android:src="@drawable/ic_favorite"
                        android:tint="@color/accent"
                        android:contentDescription="@string/favorites" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/favorites"
                        android:textAppearance="@style/TextAppearance.Body"
                        android:textAlignment="center" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- Recently Watched Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="@string/recently_watched"
            android:textAppearance="@style/TextAppearance.Title"
            android:textSize="18sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_recently_watched"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:paddingStart="0dp"
            android:paddingEnd="16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_channel_horizontal" />

        <!-- My Playlists Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="@string/my_playlists"
            android:textAppearance="@style/TextAppearance.Title"
            android:textSize="18sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_playlists"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_playlist" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
