{"logs": [{"outputFile": "com.badboyz.iptvplayer.app-mergeDebugResources-82:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\dcafc80b61e93bbcb116fecec747e0b5\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1075,1173,1285,1371,1477,1592,1670,1745,1837,1931,2027,2128,2235,2335,2439,2537,2635,2732,2814,2925,3027,3125,3232,3335,3439,3595,19607", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "1070,1168,1280,1366,1472,1587,1665,1740,1832,1926,2022,2123,2230,2330,2434,2532,2630,2727,2809,2920,3022,3120,3227,3330,3434,3590,3692,19684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4759b026881da3a3aff1b5bbcb3454af\\transformed\\navigation-ui-2.7.5\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,118", "endOffsets": "154,273"}, "to": {"startLines": "220,221", "startColumns": "4,4", "startOffsets": "18748,18852", "endColumns": "103,118", "endOffsets": "18847,18966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ec96043c8b7c92d308a76e9418d0c48a\\transformed\\material-1.10.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1089,1183,1253,1312,1420,1486,1555,1613,1685,1749,1803,1931,1991,2053,2107,2185,2322,2414,2498,2643,2727,2813,2946,3036,3115,3172,3223,3289,3363,3445,3538,3613,3687,3765,3837,3911,4021,4113,4195,4284,4373,4447,4525,4611,4666,4745,4812,4892,4976,5038,5102,5165,5234,5341,5448,5547,5653,5714,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "278,369,458,542,632,714,815,937,1018,1084,1178,1248,1307,1415,1481,1550,1608,1680,1744,1798,1926,1986,2048,2102,2180,2317,2409,2493,2638,2722,2808,2941,3031,3110,3167,3218,3284,3358,3440,3533,3608,3682,3760,3832,3906,4016,4108,4190,4279,4368,4442,4520,4606,4661,4740,4807,4887,4971,5033,5097,5160,5229,5336,5443,5542,5648,5709,5764,5846"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,4179,4270,4359,4443,4533,5346,5447,5569,9850,13891,13985,14232,14291,14399,14465,14534,14592,14664,14728,14782,14910,14970,15032,15086,15164,15301,15393,15477,15622,15706,15792,15925,16015,16094,16151,16202,16268,16342,16424,16517,16592,16666,16744,16816,16890,17000,17092,17174,17263,17352,17426,17504,17590,17645,17724,17791,17871,17955,18017,18081,18144,18213,18320,18427,18526,18632,18693,19277", "endLines": "22,54,55,56,57,58,66,67,68,107,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,226", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "965,4265,4354,4438,4528,4610,5442,5564,5645,9911,13980,14050,14286,14394,14460,14529,14587,14659,14723,14777,14905,14965,15027,15081,15159,15296,15388,15472,15617,15701,15787,15920,16010,16089,16146,16197,16263,16337,16419,16512,16587,16661,16739,16811,16885,16995,17087,17169,17258,17347,17421,17499,17585,17640,17719,17786,17866,17950,18012,18076,18139,18208,18315,18422,18521,18627,18688,18743,19354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\55604ab945968ea65ece63dd7ad46a32\\transformed\\core-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "59,60,61,62,63,64,65,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4615,4713,4815,4915,5015,5123,5228,19849", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "4708,4810,4910,5010,5118,5223,5341,19945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a20a72c21acf2a21d60cd6f37a281562\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "69,70,103,104,106,162,163,222,223,224,225,227,228,231,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5650,5746,9489,9587,9763,14055,14140,18971,19060,19148,19213,19359,19440,19689,20047,20126,20192", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "5741,5829,9582,9682,9845,14135,14227,19055,19143,19208,19272,19435,19519,19759,20121,20187,20307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\efcfa4e78b3b22010a075bf53629cfd5\\transformed\\media3-ui-1.2.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,692,779,867,942,1032,1118,1197,1262,1366,1470,1539,1609,1681,1750,1877,2005,2138,2211,2295,2371,2448,2535,2623,2689,2754,2807,2867,2915,2976,3048,3118,3183,3254,3319,3377,3443,3507,3573,3625,3687,3763,3839", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "306,496,687,774,862,937,1027,1113,1192,1257,1361,1465,1534,1604,1676,1745,1872,2000,2133,2206,2290,2366,2443,2530,2618,2684,2749,2802,2862,2910,2971,3043,3113,3178,3249,3314,3372,3438,3502,3568,3620,3682,3758,3834,3890"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,406,596,9916,10003,10091,10166,10256,10342,10421,10486,10590,10694,10763,10833,10905,10974,11101,11229,11362,11435,11519,11595,11672,11759,11847,11913,12673,12726,12786,12834,12895,12967,13037,13102,13173,13238,13296,13362,13426,13492,13544,13606,13682,13758", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "401,591,782,9998,10086,10161,10251,10337,10416,10481,10585,10689,10758,10828,10900,10969,11096,11224,11357,11430,11514,11590,11667,11754,11842,11908,11973,12721,12781,12829,12890,12962,13032,13097,13168,13233,13291,13357,13421,13487,13539,13601,13677,13753,13809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b89cf8e779a00d2af6b7386c87a67af\\transformed\\material3-1.1.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,307,412,537,615,707,816,956,1070,1222,1303,1404,1495,1593,1707,1829,1930,2062,2195,2326,2501,2625,2744,2869,2991,3083,3177,3293,3418,3514,3615,3713,3849,3993,4095,4192,4268,4345,4428,4513,4610,4686,4768,4867,4969,5060,5157,5241,5346,5443,5542,5659,5735,5836", "endColumns": "126,124,104,124,77,91,108,139,113,151,80,100,90,97,113,121,100,131,132,130,174,123,118,124,121,91,93,115,124,95,100,97,135,143,101,96,75,76,82,84,96,75,81,98,101,90,96,83,104,96,98,116,75,100,91", "endOffsets": "177,302,407,532,610,702,811,951,1065,1217,1298,1399,1490,1588,1702,1824,1925,2057,2190,2321,2496,2620,2739,2864,2986,3078,3172,3288,3413,3509,3610,3708,3844,3988,4090,4187,4263,4340,4423,4508,4605,4681,4763,4862,4964,5055,5152,5236,5341,5438,5537,5654,5730,5831,5923"}, "to": {"startLines": "50,51,52,53,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,105,159,229,232,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3697,3824,3949,4054,5834,5912,6004,6113,6253,6367,6519,6600,6701,6792,6890,7004,7126,7227,7359,7492,7623,7798,7922,8041,8166,8288,8380,8474,8590,8715,8811,8912,9010,9146,9290,9392,9687,13814,19524,19764,19950,20312,20388,20470,20569,20671,20762,20859,20943,21048,21145,21244,21361,21437,21538", "endColumns": "126,124,104,124,77,91,108,139,113,151,80,100,90,97,113,121,100,131,132,130,174,123,118,124,121,91,93,115,124,95,100,97,135,143,101,96,75,76,82,84,96,75,81,98,101,90,96,83,104,96,98,116,75,100,91", "endOffsets": "3819,3944,4049,4174,5907,5999,6108,6248,6362,6514,6595,6696,6787,6885,6999,7121,7222,7354,7487,7618,7793,7917,8036,8161,8283,8375,8469,8585,8710,8806,8907,9005,9141,9285,9387,9484,9758,13886,19602,19844,20042,20383,20465,20564,20666,20757,20854,20938,21043,21140,21239,21356,21432,21533,21625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\98ebe932c34efba361490b6fec49f11d\\transformed\\media3-exoplayer-1.2.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,270,342,419,486,583,674", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "124,191,265,337,414,481,578,669,745"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11978,12052,12119,12193,12265,12342,12409,12506,12597", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "12047,12114,12188,12260,12337,12404,12501,12592,12668"}}]}]}